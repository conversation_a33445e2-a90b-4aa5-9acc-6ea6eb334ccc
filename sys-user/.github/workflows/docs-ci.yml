name: 📚 Documentation CI/CD

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'src/**'
      - 'package.json'
      - 'README.md'
      - 'docs/**'
      - '.github/workflows/docs-ci.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'src/**'
      - 'package.json'
      - 'README.md'
      - 'docs/**'

jobs:
  # 🔍 文档验证和同步检查
  docs-validation:
    name: 📋 文档验证
    runs-on: ubuntu-latest
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 🟢 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: 📦 安装依赖
      run: npm ci
    
    - name: 🔍 TypeScript 类型检查
      run: npm run typecheck
    
    - name: 🧹 代码质量检查
      run: npm run lint
    
    - name: 🧪 运行测试
      run: npm test
    
    - name: 📊 生成测试覆盖率报告
      run: npm run test:coverage || echo "Coverage script not found"
    
    - name: 🔍 检查文档链接
      uses: gaurav-nelson/github-action-markdown-link-check@v1
      with:
        use-quiet-mode: 'yes'
        use-verbose-mode: 'yes'
        config-file: '.github/markdown-link-check-config.json'
    
    - name: 📝 验证 API 文档同步
      run: |
        echo "🔍 检查 API 文档是否与代码同步..."
        node scripts/validate-api-docs.js || echo "API 文档验证脚本未找到，跳过检查"

  # 🚀 自动生成和更新文档
  docs-generation:
    name: 🔄 文档生成
    runs-on: ubuntu-latest
    needs: docs-validation
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0
    
    - name: 🟢 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: 📦 安装依赖
      run: npm ci
    
    - name: 🏗️ 构建项目
      run: npm run build
    
    - name: 📚 生成 API 文档
      run: |
        echo "🔄 生成 API 文档..."
        npm run docs:generate || echo "文档生成脚本未找到"
    
    - name: 📊 生成项目统计
      run: |
        echo "📊 更新项目统计信息..."
        node scripts/update-project-stats.js || echo "统计脚本未找到"
    
    - name: 🔄 更新版本信息
      run: |
        echo "🔄 更新文档版本信息..."
        CURRENT_DATE=$(date '+%Y年%m月%d日')
        VERSION=$(node -p "require('./package.json').version")
        sed -i "s/\*\*版本\*\*: .*/\*\*版本\*\*: $VERSION/" README.md
        sed -i "s/\*\*最后更新\*\*: .*/\*\*最后更新\*\*: $CURRENT_DATE/" README.md
    
    - name: 💾 提交文档更新
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add .
        if git diff --staged --quiet; then
          echo "📝 没有文档更改需要提交"
        else
          git commit -m "📚 自动更新文档 [skip ci]"
          git push
        fi

  # 🌐 部署文档站点
  docs-deployment:
    name: 🚀 文档部署
    runs-on: ubuntu-latest
    needs: [docs-validation, docs-generation]
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 🟢 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: 📦 安装依赖
      run: npm ci
    
    - name: 🏗️ 构建文档站点
      run: |
        echo "🏗️ 构建文档站点..."
        npm run docs:build || echo "文档构建脚本未找到"
    
    - name: 🚀 部署到 GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      if: github.ref == 'refs/heads/main'
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./docs/dist
        cname: docs.your-domain.com  # 可选：自定义域名

  # 📊 文档质量报告
  docs-quality-report:
    name: 📊 文档质量报告
    runs-on: ubuntu-latest
    needs: docs-validation
    
    steps:
    - name: 📥 检出代码
      uses: actions/checkout@v4
    
    - name: 🟢 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: 📦 安装依赖
      run: npm ci
    
    - name: 📊 生成文档质量报告
      run: |
        echo "📊 生成文档质量报告..."
        node scripts/docs-quality-check.js || echo "质量检查脚本未找到"
    
    - name: 📝 创建 PR 评论
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const reportPath = './docs-quality-report.md';
          
          if (fs.existsSync(reportPath)) {
            const report = fs.readFileSync(reportPath, 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 📊 文档质量报告\n\n${report}`
            });
          }
