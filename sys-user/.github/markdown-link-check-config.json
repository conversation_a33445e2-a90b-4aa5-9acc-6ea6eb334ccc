{"ignorePatterns": [{"pattern": "^http://localhost"}, {"pattern": "^https://localhost"}, {"pattern": "^http://127.0.0.1"}, {"pattern": "^https://127.0.0.1"}], "replacementPatterns": [{"pattern": "^/", "replacement": "{{BASEURL}}/"}], "httpHeaders": [{"urls": ["https://github.com"], "headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8", "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}}], "timeout": "20s", "retryOn429": true, "retryCount": 3, "fallbackRetryDelay": "30s", "aliveStatusCodes": [200, 206]}