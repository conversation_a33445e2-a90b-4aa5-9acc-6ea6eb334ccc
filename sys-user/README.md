# 通用用户管理系统

基于Node.js + TypeScript + MySQL的企业级用户管理系统，支持完整的RBAC权限控制。

## 🎯 系统特性

### 核心功能
- ✅ 用户注册、登录、登出
- ✅ JWT Token + Refresh Token认证机制
- ✅ 基于RBAC的角色权限控制
- ✅ 用户信息管理（CRUD）
- ✅ 密码安全管理（加盐哈希）
- ✅ 会话管理与控制
- ✅ 操作审计日志
- ✅ API限流保护

### 安全特性
- 🔒 密码强度验证
- 🔒 防暴力破解（登录限流）
- 🔒 JWT令牌安全管理
- 🔒 CORS跨域保护
- 🔒 Helmet安全头
- 🔒 输入验证与过滤
- 🔒 SQL注入防护

### 权限管理
- 👥 多角色支持（超级管理员、管理员、普通用户）
- 🎛️ 细粒度权限控制
- 📋 动态权限分配
- ⏰ 角色过期机制
- 🔄 权限继承体系

## 📁 项目结构

```
src/
├── config/          # 配置文件
│   └── database.ts  # 数据库连接配置
├── controllers/     # 控制器层
│   ├── user.controller.ts
│   ├── role.controller.ts
│   └── permission.controller.ts
├── middleware/      # 中间件
│   ├── auth.middleware.ts
│   ├── rate-limit.middleware.ts
│   └── error.middleware.ts
├── models/          # 数据模型层
│   ├── user.model.ts
│   ├── role.model.ts
│   ├── permission.model.ts
│   ├── session.model.ts
│   └── audit-log.model.ts
├── routes/          # 路由定义
│   ├── auth.routes.ts
│   ├── user.routes.ts
│   ├── role.routes.ts
│   └── permission.routes.ts
├── services/        # 业务逻辑层
│   ├── auth.service.ts
│   └── authorization.service.ts
├── types/           # TypeScript类型定义
│   └── user.types.ts
├── utils/           # 工具函数
│   └── auth.utils.ts
├── validators/      # 数据验证
│   └── user.validators.ts
├── app.ts          # Express应用配置
└── index.ts        # 应用入口
```

## 🚀 快速开始

### 环境要求
- Node.js 16+
- MySQL 8.0+
- TypeScript 4.9+

### 安装依赖
```bash
npm install
```

### 环境配置
```bash
cp .env.example .env
# 编辑.env文件，配置数据库连接等信息
```

### 数据库初始化
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE user_management;

# 导入架构
mysql -u root -p user_management < database/schema.sql
```

### 启动服务
```bash
# 开发模式
npm run dev

# 生产模式
npm run build
npm start
```

## 📚 API文档

### 认证接口

#### 用户注册
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "Password123!",
  "fullName": "Test User"
}
```

#### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "usernameOrEmail": "testuser",
  "password": "Password123!"
}
```

#### 获取当前用户
```http
GET /api/v1/auth/me
Authorization: Bearer <access_token>
```

### 用户管理接口

#### 获取用户列表
```http
GET /api/v1/users?page=1&limit=20&status=active
Authorization: Bearer <access_token>
```

#### 更新用户信息
```http
PUT /api/v1/users/123
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "fullName": "Updated Name",
  "status": "active"
}
```

### 角色管理接口

#### 创建角色
```http
POST /api/v1/roles
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "name": "editor",
  "displayName": "内容编辑",
  "description": "负责内容编辑的角色"
}
```

#### 分配权限给角色
```http
POST /api/v1/roles/123/permissions
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "permissionIds": [1, 2, 3]
}
```

## 🛡️ 权限系统

### 默认角色
- **super_admin**: 超级管理员，拥有所有权限
- **admin**: 管理员，可管理用户和角色
- **user**: 普通用户，基础权限

### 权限粒度
权限按照 `资源.动作` 的格式定义：
- `user.create` - 创建用户
- `user.read` - 查看用户
- `user.update` - 更新用户
- `user.delete` - 删除用户
- `role.manage` - 管理角色
- `permission.view` - 查看权限

### 使用示例
```typescript
// 检查权限
await AuthorizationService.hasPermission(userId, 'user', 'create');

// 检查角色
await AuthorizationService.hasRole(userId, 'admin');

// 分配角色
await AuthorizationService.assignRoles(userId, [roleId1, roleId2]);
```

## 🔧 配置说明

### 数据库配置
```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=user_management
```

### JWT配置
```env
JWT_SECRET=your-secret-key
JWT_REFRESH_SECRET=your-refresh-secret
ACCESS_TOKEN_EXPIRY=15m
REFRESH_TOKEN_EXPIRY=7d
```

### 安全配置
```env
BCRYPT_SALT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
```

## 📊 数据库设计

### 核心表结构
- `users` - 用户表
- `roles` - 角色表
- `permissions` - 权限表
- `user_roles` - 用户角色关联表
- `role_permissions` - 角色权限关联表
- `user_sessions` - 用户会话表
- `audit_logs` - 审计日志表

### 关系图
```
users ←→ user_roles ←→ roles
                       ↓
                role_permissions
                       ↓
                  permissions
```

## 🧪 测试

```bash
# 运行测试
npm test

# 运行类型检查
npm run typecheck

# 运行代码检查
npm run lint
```

## 📈 性能优化

### 数据库优化
- 合理的索引设计
- 连接池管理
- 查询优化

### 安全优化
- API限流
- 会话清理
- 密码策略

### 缓存策略
- JWT令牌缓存
- 权限缓存
- 会话缓存

## 🔄 部署

### Docker部署
```dockerfile
FROM node:16-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3000
CMD ["node", "dist/index.js"]
```

### 环境变量
生产环境需要设置的关键环境变量：
- `NODE_ENV=production`
- `JWT_SECRET` (强密钥)
- `DB_PASSWORD` (安全密码)

## 🤝 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🆘 支持

如有问题或建议，请提交 Issue 或联系开发团队。

---

**版本**: 1.0.0
**最后更新**: 2024年
**文档状态**: 🟢 自动同步 | **构建**: ![Build Status](https://github.com/your-org/user-management-system/workflows/docs/badge.svg)