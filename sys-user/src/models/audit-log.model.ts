import { database } from '../config/database';
import { AuditLog } from '../types/user.types';

export interface CreateAuditLogInput {
  userId?: number;
  action: string;
  resourceType?: string;
  resourceId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
}

export class AuditLogModel {
  static async create(input: CreateAuditLogInput): Promise<AuditLog> {
    const query = `
      INSERT INTO audit_logs (user_id, action, resource_type, resource_id, details, ip_address, user_agent)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    
    const [result] = await database.execute(query, [
      input.userId || null,
      input.action,
      input.resourceType || null,
      input.resourceId || null,
      input.details ? JSON.stringify(input.details) : null,
      input.ipAddress || null,
      input.userAgent || null
    ]);
    
    const insertResult = result as any;
    const auditLog = await this.findById(insertResult.insertId);
    
    if (!auditLog) {
      throw new Error('Failed to create audit log');
    }
    
    return auditLog;
  }

  static async findById(id: number): Promise<AuditLog | null> {
    const query = `
      SELECT id, user_id as userId, action, resource_type as resourceType, 
             resource_id as resourceId, details, ip_address as ipAddress, 
             user_agent as userAgent, created_at as createdAt
      FROM audit_logs
      WHERE id = ?
    `;
    
    const result = await database.executeSingle<any>(query, [id]);
    if (!result) return null;

    return {
      ...result,
      details: result.details ? JSON.parse(result.details) : null
    };
  }

  static async findByUserId(userId: number, offset = 0, limit = 50): Promise<AuditLog[]> {
    const query = `
      SELECT id, user_id as userId, action, resource_type as resourceType, 
             resource_id as resourceId, details, ip_address as ipAddress, 
             user_agent as userAgent, created_at as createdAt
      FROM audit_logs
      WHERE user_id = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const results = await database.execute<any>(query, [userId, limit, offset]);
    return results.map(row => ({
      ...row,
      details: row.details ? JSON.parse(row.details) : null
    }));
  }

  static async findByAction(action: string, offset = 0, limit = 50): Promise<AuditLog[]> {
    const query = `
      SELECT id, user_id as userId, action, resource_type as resourceType, 
             resource_id as resourceId, details, ip_address as ipAddress, 
             user_agent as userAgent, created_at as createdAt
      FROM audit_logs
      WHERE action = ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const results = await database.execute<any>(query, [action, limit, offset]);
    return results.map(row => ({
      ...row,
      details: row.details ? JSON.parse(row.details) : null
    }));
  }

  static async findByResource(resourceType: string, resourceId?: string, offset = 0, limit = 50): Promise<AuditLog[]> {
    let query = `
      SELECT id, user_id as userId, action, resource_type as resourceType, 
             resource_id as resourceId, details, ip_address as ipAddress, 
             user_agent as userAgent, created_at as createdAt
      FROM audit_logs
      WHERE resource_type = ?
    `;
    
    const params = [resourceType];
    
    if (resourceId) {
      query += ' AND resource_id = ?';
      params.push(resourceId);
    }
    
    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);
    
    const results = await database.execute<any>(query, params);
    return results.map(row => ({
      ...row,
      details: row.details ? JSON.parse(row.details) : null
    }));
  }

  static async findByDateRange(startDate: Date, endDate: Date, offset = 0, limit = 50): Promise<AuditLog[]> {
    const query = `
      SELECT id, user_id as userId, action, resource_type as resourceType, 
             resource_id as resourceId, details, ip_address as ipAddress, 
             user_agent as userAgent, created_at as createdAt
      FROM audit_logs
      WHERE created_at BETWEEN ? AND ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const results = await database.execute<any>(query, [startDate, endDate, limit, offset]);
    return results.map(row => ({
      ...row,
      details: row.details ? JSON.parse(row.details) : null
    }));
  }

  static async list(offset = 0, limit = 50): Promise<AuditLog[]> {
    const query = `
      SELECT id, user_id as userId, action, resource_type as resourceType, 
             resource_id as resourceId, details, ip_address as ipAddress, 
             user_agent as userAgent, created_at as createdAt
      FROM audit_logs
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const results = await database.execute<any>(query, [limit, offset]);
    return results.map(row => ({
      ...row,
      details: row.details ? JSON.parse(row.details) : null
    }));
  }

  static async count(): Promise<number> {
    const query = 'SELECT COUNT(*) as count FROM audit_logs';
    const result = await database.executeSingle<{ count: number }>(query);
    return result?.count || 0;
  }

  static async countByUserId(userId: number): Promise<number> {
    const query = 'SELECT COUNT(*) as count FROM audit_logs WHERE user_id = ?';
    const result = await database.executeSingle<{ count: number }>(query, [userId]);
    return result?.count || 0;
  }

  static async countByAction(action: string): Promise<number> {
    const query = 'SELECT COUNT(*) as count FROM audit_logs WHERE action = ?';
    const result = await database.executeSingle<{ count: number }>(query, [action]);
    return result?.count || 0;
  }

  static async deleteOldLogs(daysToKeep = 90): Promise<number> {
    const query = 'DELETE FROM audit_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)';
    const result = await database.execute(query, [daysToKeep]);
    return (result as any).affectedRows;
  }

  static async getActionSummary(startDate?: Date, endDate?: Date): Promise<Array<{ action: string; count: number }>> {
    let query = `
      SELECT action, COUNT(*) as count
      FROM audit_logs
    `;
    
    const params: any[] = [];
    
    if (startDate && endDate) {
      query += ' WHERE created_at BETWEEN ? AND ?';
      params.push(startDate, endDate);
    }
    
    query += ' GROUP BY action ORDER BY count DESC';
    
    return database.execute<{ action: string; count: number }>(query, params);
  }

  static async getUserActivity(userId: number, days = 30): Promise<Array<{ date: string; count: number }>> {
    const query = `
      SELECT DATE(created_at) as date, COUNT(*) as count
      FROM audit_logs
      WHERE user_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY DATE(created_at)
      ORDER BY date DESC
    `;
    
    return database.execute<{ date: string; count: number }>(query, [userId, days]);
  }

  // 便捷方法：记录用户登录
  static async logUserLogin(userId: number, ipAddress?: string, userAgent?: string): Promise<AuditLog> {
    return this.create({
      userId,
      action: 'user.login',
      resourceType: 'user',
      resourceId: userId.toString(),
      ipAddress,
      userAgent
    });
  }

  // 便捷方法：记录用户登出
  static async logUserLogout(userId: number, ipAddress?: string, userAgent?: string): Promise<AuditLog> {
    return this.create({
      userId,
      action: 'user.logout',
      resourceType: 'user',
      resourceId: userId.toString(),
      ipAddress,
      userAgent
    });
  }

  // 便捷方法：记录用户创建
  static async logUserCreate(creatorId: number, targetUserId: number, ipAddress?: string, userAgent?: string): Promise<AuditLog> {
    return this.create({
      userId: creatorId,
      action: 'user.create',
      resourceType: 'user',
      resourceId: targetUserId.toString(),
      ipAddress,
      userAgent
    });
  }

  // 便捷方法：记录角色分配
  static async logRoleAssign(assignerId: number, targetUserId: number, roleId: number, ipAddress?: string, userAgent?: string): Promise<AuditLog> {
    return this.create({
      userId: assignerId,
      action: 'role.assign',
      resourceType: 'user_role',
      resourceId: `${targetUserId}:${roleId}`,
      details: { targetUserId, roleId },
      ipAddress,
      userAgent
    });
  }
}