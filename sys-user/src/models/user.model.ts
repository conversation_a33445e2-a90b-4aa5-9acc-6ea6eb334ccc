import { database } from '../config/database';
import { AuthUtils } from '../utils/auth.utils';
import {
  User,
  UserWithPassword,
  CreateUserInput,
  UpdateUserInput,
  LoginInput,
  AuthResult
} from '../types/user.types';

export class UserModel {
  static async findById(id: number): Promise<User | null> {
    const query = `
      SELECT id, username, email, full_name as fullName, phone, avatar_url as avatarUrl,
             status, last_login as lastLogin, created_at as createdAt, updated_at as updatedAt
      FROM users 
      WHERE id = ?
    `;
    return database.executeSingle<User>(query, [id]);
  }

  static async findByUsername(username: string): Promise<User | null> {
    const query = `
      SELECT id, username, email, full_name as fullName, phone, avatar_url as avatarUrl,
             status, last_login as lastLogin, created_at as createdAt, updated_at as updatedAt
      FROM users 
      WHERE username = ?
    `;
    return database.executeSingle<User>(query, [username]);
  }

  static async findByEmail(email: string): Promise<User | null> {
    const query = `
      SELECT id, username, email, full_name as fullName, phone, avatar_url as avatarUrl,
             status, last_login as lastLogin, created_at as createdAt, updated_at as updatedAt
      FROM users 
      WHERE email = ?
    `;
    return database.executeSingle<User>(query, [email]);
  }

  static async findByUsernameOrEmail(usernameOrEmail: string): Promise<UserWithPassword | null> {
    const query = `
      SELECT id, username, email, password_hash as passwordHash, salt,
             full_name as fullName, phone, avatar_url as avatarUrl,
             status, last_login as lastLogin, created_at as createdAt, updated_at as updatedAt
      FROM users 
      WHERE username = ? OR email = ?
    `;
    return database.executeSingle<UserWithPassword>(query, [usernameOrEmail, usernameOrEmail]);
  }

  static async create(input: CreateUserInput): Promise<User> {
    const { hash, salt } = await AuthUtils.hashPassword(input.password);
    
    const query = `
      INSERT INTO users (username, email, password_hash, salt, full_name, phone, avatar_url)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    
    const connection = await database.beginTransaction();
    
    try {
      const [result] = await connection.execute(query, [
        input.username,
        input.email,
        hash,
        salt,
        input.fullName || null,
        input.phone || null,
        input.avatarUrl || null
      ]);
      
      const insertResult = result as any;
      const userId = insertResult.insertId;
      
      // 给新用户分配默认用户角色
      const roleQuery = `
        INSERT INTO user_roles (user_id, role_id)
        SELECT ?, id FROM roles WHERE name = 'user'
      `;
      await connection.execute(roleQuery, [userId]);
      
      await database.commitTransaction(connection);
      
      const user = await this.findById(userId);
      if (!user) {
        throw new Error('Failed to create user');
      }
      
      return user;
    } catch (error) {
      await database.rollbackTransaction(connection);
      throw error;
    }
  }

  static async update(id: number, input: UpdateUserInput): Promise<User | null> {
    const setParts: string[] = [];
    const values: any[] = [];
    
    if (input.fullName !== undefined) {
      setParts.push('full_name = ?');
      values.push(input.fullName);
    }
    
    if (input.phone !== undefined) {
      setParts.push('phone = ?');
      values.push(input.phone);
    }
    
    if (input.avatarUrl !== undefined) {
      setParts.push('avatar_url = ?');
      values.push(input.avatarUrl);
    }
    
    if (input.status !== undefined) {
      setParts.push('status = ?');
      values.push(input.status);
    }
    
    if (setParts.length === 0) {
      return this.findById(id);
    }
    
    values.push(id);
    
    const query = `
      UPDATE users 
      SET ${setParts.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    await database.execute(query, values);
    return this.findById(id);
  }

  static async delete(id: number): Promise<boolean> {
    const query = 'DELETE FROM users WHERE id = ?';
    const result = await database.execute(query, [id]);
    return (result as any).affectedRows > 0;
  }

  static async updateLastLogin(id: number): Promise<void> {
    const query = 'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?';
    await database.execute(query, [id]);
  }

  static async changePassword(id: number, newPassword: string): Promise<boolean> {
    const { hash, salt } = await AuthUtils.hashPassword(newPassword);
    
    const query = `
      UPDATE users 
      SET password_hash = ?, salt = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    const result = await database.execute(query, [hash, salt, id]);
    return (result as any).affectedRows > 0;
  }

  static async validateCredentials(input: LoginInput): Promise<UserWithPassword | null> {
    const user = await this.findByUsernameOrEmail(input.usernameOrEmail);
    
    if (!user || user.status !== 'active') {
      return null;
    }
    
    const isValidPassword = await AuthUtils.verifyPassword(
      input.password,
      user.passwordHash,
      user.salt
    );
    
    return isValidPassword ? user : null;
  }

  static async list(offset = 0, limit = 20, status?: string): Promise<User[]> {
    let query = `
      SELECT id, username, email, full_name as fullName, phone, avatar_url as avatarUrl,
             status, last_login as lastLogin, created_at as createdAt, updated_at as updatedAt
      FROM users
    `;
    
    const values: any[] = [];
    
    if (status) {
      query += ' WHERE status = ?';
      values.push(status);
    }
    
    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    values.push(limit, offset);
    
    return database.execute<User>(query, values);
  }

  static async count(status?: string): Promise<number> {
    let query = 'SELECT COUNT(*) as count FROM users';
    const values: any[] = [];
    
    if (status) {
      query += ' WHERE status = ?';
      values.push(status);
    }
    
    const result = await database.executeSingle<{ count: number }>(query, values);
    return result?.count || 0;
  }

  static async search(keyword: string, offset = 0, limit = 20): Promise<User[]> {
    const query = `
      SELECT id, username, email, full_name as fullName, phone, avatar_url as avatarUrl,
             status, last_login as lastLogin, created_at as createdAt, updated_at as updatedAt
      FROM users
      WHERE username LIKE ? OR email LIKE ? OR full_name LIKE ?
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const searchTerm = `%${keyword}%`;
    return database.execute<User>(query, [searchTerm, searchTerm, searchTerm, limit, offset]);
  }

  static async exists(username: string, email: string, excludeId?: number): Promise<boolean> {
    let query = `
      SELECT COUNT(*) as count 
      FROM users 
      WHERE (username = ? OR email = ?)
    `;
    
    const values = [username, email];
    
    if (excludeId) {
      query += ' AND id != ?';
      values.push(excludeId);
    }
    
    const result = await database.executeSingle<{ count: number }>(query, values);
    return (result?.count || 0) > 0;
  }
}