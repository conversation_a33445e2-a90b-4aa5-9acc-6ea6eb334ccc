import { database } from '../config/database';
import { Role, Permission } from '../types/user.types';

export interface CreateRoleInput {
  name: string;
  displayName: string;
  description?: string;
}

export interface UpdateRoleInput {
  displayName?: string;
  description?: string;
}

export class RoleModel {
  static async findById(id: number): Promise<Role | null> {
    const query = `
      SELECT id, name, display_name as displayName, description, is_system as isSystem,
             created_at as createdAt, updated_at as updatedAt
      FROM roles 
      WHERE id = ?
    `;
    return database.executeSingle<Role>(query, [id]);
  }

  static async findByName(name: string): Promise<Role | null> {
    const query = `
      SELECT id, name, display_name as displayName, description, is_system as isSystem,
             created_at as createdAt, updated_at as updatedAt
      FROM roles 
      WHERE name = ?
    `;
    return database.executeSingle<Role>(query, [name]);
  }

  static async create(input: CreateRoleInput): Promise<Role> {
    const query = `
      INSERT INTO roles (name, display_name, description)
      VALUES (?, ?, ?)
    `;
    
    const [result] = await database.execute(query, [
      input.name,
      input.displayName,
      input.description || null
    ]);
    
    const insertResult = result as any;
    const role = await this.findById(insertResult.insertId);
    
    if (!role) {
      throw new Error('Failed to create role');
    }
    
    return role;
  }

  static async update(id: number, input: UpdateRoleInput): Promise<Role | null> {
    const setParts: string[] = [];
    const values: any[] = [];
    
    if (input.displayName !== undefined) {
      setParts.push('display_name = ?');
      values.push(input.displayName);
    }
    
    if (input.description !== undefined) {
      setParts.push('description = ?');
      values.push(input.description);
    }
    
    if (setParts.length === 0) {
      return this.findById(id);
    }
    
    values.push(id);
    
    const query = `
      UPDATE roles 
      SET ${setParts.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ? AND is_system = FALSE
    `;
    
    await database.execute(query, values);
    return this.findById(id);
  }

  static async delete(id: number): Promise<boolean> {
    const query = 'DELETE FROM roles WHERE id = ? AND is_system = FALSE';
    const result = await database.execute(query, [id]);
    return (result as any).affectedRows > 0;
  }

  static async list(): Promise<Role[]> {
    const query = `
      SELECT id, name, display_name as displayName, description, is_system as isSystem,
             created_at as createdAt, updated_at as updatedAt
      FROM roles
      ORDER BY is_system DESC, name ASC
    `;
    return database.execute<Role>(query);
  }

  static async getPermissions(roleId: number): Promise<Permission[]> {
    const query = `
      SELECT p.id, p.name, p.display_name as displayName, p.resource, p.action, 
             p.description, p.created_at as createdAt
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      WHERE rp.role_id = ?
      ORDER BY p.resource, p.action
    `;
    return database.execute<Permission>(query, [roleId]);
  }

  static async assignPermission(roleId: number, permissionId: number): Promise<boolean> {
    const query = `
      INSERT IGNORE INTO role_permissions (role_id, permission_id)
      VALUES (?, ?)
    `;
    
    const result = await database.execute(query, [roleId, permissionId]);
    return (result as any).affectedRows > 0;
  }

  static async revokePermission(roleId: number, permissionId: number): Promise<boolean> {
    const query = `
      DELETE FROM role_permissions 
      WHERE role_id = ? AND permission_id = ?
    `;
    
    const result = await database.execute(query, [roleId, permissionId]);
    return (result as any).affectedRows > 0;
  }

  static async assignPermissions(roleId: number, permissionIds: number[]): Promise<void> {
    if (permissionIds.length === 0) return;
    
    const connection = await database.beginTransaction();
    
    try {
      // 删除现有权限
      await connection.execute('DELETE FROM role_permissions WHERE role_id = ?', [roleId]);
      
      // 分配新权限
      const values = permissionIds.map(permissionId => [roleId, permissionId]);
      const placeholders = values.map(() => '(?, ?)').join(', ');
      const flatValues = values.flat();
      
      await connection.execute(
        `INSERT INTO role_permissions (role_id, permission_id) VALUES ${placeholders}`,
        flatValues
      );
      
      await database.commitTransaction(connection);
    } catch (error) {
      await database.rollbackTransaction(connection);
      throw error;
    }
  }

  static async exists(name: string, excludeId?: number): Promise<boolean> {
    let query = 'SELECT COUNT(*) as count FROM roles WHERE name = ?';
    const values = [name];
    
    if (excludeId) {
      query += ' AND id != ?';
      values.push(excludeId);
    }
    
    const result = await database.executeSingle<{ count: number }>(query, values);
    return (result?.count || 0) > 0;
  }
}