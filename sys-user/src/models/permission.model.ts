import { database } from '../config/database';
import { Permission } from '../types/user.types';

export interface CreatePermissionInput {
  name: string;
  displayName: string;
  resource: string;
  action: string;
  description?: string;
}

export interface UpdatePermissionInput {
  displayName?: string;
  description?: string;
}

export class PermissionModel {
  static async findById(id: number): Promise<Permission | null> {
    const query = `
      SELECT id, name, display_name as displayName, resource, action, 
             description, created_at as createdAt
      FROM permissions 
      WHERE id = ?
    `;
    return database.executeSingle<Permission>(query, [id]);
  }

  static async findByName(name: string): Promise<Permission | null> {
    const query = `
      SELECT id, name, display_name as displayName, resource, action, 
             description, created_at as createdAt
      FROM permissions 
      WHERE name = ?
    `;
    return database.executeSingle<Permission>(query, [name]);
  }

  static async create(input: CreatePermissionInput): Promise<Permission> {
    const query = `
      INSERT INTO permissions (name, display_name, resource, action, description)
      VALUES (?, ?, ?, ?, ?)
    `;
    
    const [result] = await database.execute(query, [
      input.name,
      input.displayName,
      input.resource,
      input.action,
      input.description || null
    ]);
    
    const insertResult = result as any;
    const permission = await this.findById(insertResult.insertId);
    
    if (!permission) {
      throw new Error('Failed to create permission');
    }
    
    return permission;
  }

  static async update(id: number, input: UpdatePermissionInput): Promise<Permission | null> {
    const setParts: string[] = [];
    const values: any[] = [];
    
    if (input.displayName !== undefined) {
      setParts.push('display_name = ?');
      values.push(input.displayName);
    }
    
    if (input.description !== undefined) {
      setParts.push('description = ?');
      values.push(input.description);
    }
    
    if (setParts.length === 0) {
      return this.findById(id);
    }
    
    values.push(id);
    
    const query = `
      UPDATE permissions 
      SET ${setParts.join(', ')}
      WHERE id = ?
    `;
    
    await database.execute(query, values);
    return this.findById(id);
  }

  static async delete(id: number): Promise<boolean> {
    const query = 'DELETE FROM permissions WHERE id = ?';
    const result = await database.execute(query, [id]);
    return (result as any).affectedRows > 0;
  }

  static async list(): Promise<Permission[]> {
    const query = `
      SELECT id, name, display_name as displayName, resource, action, 
             description, created_at as createdAt
      FROM permissions
      ORDER BY resource, action
    `;
    return database.execute<Permission>(query);
  }

  static async findByResource(resource: string): Promise<Permission[]> {
    const query = `
      SELECT id, name, display_name as displayName, resource, action, 
             description, created_at as createdAt
      FROM permissions
      WHERE resource = ?
      ORDER BY action
    `;
    return database.execute<Permission>(query, [resource]);
  }

  static async findByResourceAndAction(resource: string, action: string): Promise<Permission | null> {
    const query = `
      SELECT id, name, display_name as displayName, resource, action, 
             description, created_at as createdAt
      FROM permissions
      WHERE resource = ? AND action = ?
    `;
    return database.executeSingle<Permission>(query, [resource, action]);
  }

  static async exists(name: string, excludeId?: number): Promise<boolean> {
    let query = 'SELECT COUNT(*) as count FROM permissions WHERE name = ?';
    const values = [name];
    
    if (excludeId) {
      query += ' AND id != ?';
      values.push(excludeId);
    }
    
    const result = await database.executeSingle<{ count: number }>(query, values);
    return (result?.count || 0) > 0;
  }

  static async getResources(): Promise<string[]> {
    const query = 'SELECT DISTINCT resource FROM permissions ORDER BY resource';
    const result = await database.execute<{ resource: string }>(query);
    return result.map(row => row.resource);
  }

  static async getActions(resource?: string): Promise<string[]> {
    let query = 'SELECT DISTINCT action FROM permissions';
    const values: any[] = [];
    
    if (resource) {
      query += ' WHERE resource = ?';
      values.push(resource);
    }
    
    query += ' ORDER BY action';
    
    const result = await database.execute<{ action: string }>(query, values);
    return result.map(row => row.action);
  }
}