import { database } from '../config/database';
import { AuthUtils } from '../utils/auth.utils';
import { User, AuthResult } from '../types/user.types';

export interface UserSession {
  id: number;
  userId: number;
  tokenHash: string;
  refreshTokenHash: string;
  expiresAt: Date;
  createdAt: Date;
  lastAccessed: Date;
  userAgent?: string;
  ipAddress?: string;
}

export interface CreateSessionInput {
  userId: number;
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
  userAgent?: string;
  ipAddress?: string;
}

export class SessionModel {
  static async create(input: CreateSessionInput): Promise<UserSession> {
    const tokenHash = AuthUtils.hashToken(input.accessToken);
    const refreshTokenHash = AuthUtils.hashToken(input.refreshToken);
    
    const query = `
      INSERT INTO user_sessions (user_id, token_hash, refresh_token_hash, expires_at, user_agent, ip_address)
      VALUES (?, ?, ?, ?, ?, ?)
    `;
    
    const [result] = await database.execute(query, [
      input.userId,
      tokenHash,
      refreshTokenHash,
      input.expiresAt,
      input.userAgent || null,
      input.ipAddress || null
    ]);
    
    const insertResult = result as any;
    const session = await this.findById(insertResult.insertId);
    
    if (!session) {
      throw new Error('Failed to create session');
    }
    
    return session;
  }

  static async findById(id: number): Promise<UserSession | null> {
    const query = `
      SELECT id, user_id as userId, token_hash as tokenHash, refresh_token_hash as refreshTokenHash,
             expires_at as expiresAt, created_at as createdAt, last_accessed as lastAccessed,
             user_agent as userAgent, ip_address as ipAddress
      FROM user_sessions
      WHERE id = ?
    `;
    return database.executeSingle<UserSession>(query, [id]);
  }

  static async findByToken(token: string): Promise<UserSession | null> {
    const tokenHash = AuthUtils.hashToken(token);
    const query = `
      SELECT id, user_id as userId, token_hash as tokenHash, refresh_token_hash as refreshTokenHash,
             expires_at as expiresAt, created_at as createdAt, last_accessed as lastAccessed,
             user_agent as userAgent, ip_address as ipAddress
      FROM user_sessions
      WHERE token_hash = ? AND expires_at > NOW()
    `;
    return database.executeSingle<UserSession>(query, [tokenHash]);
  }

  static async findByRefreshToken(refreshToken: string): Promise<UserSession | null> {
    const refreshTokenHash = AuthUtils.hashToken(refreshToken);
    const query = `
      SELECT id, user_id as userId, token_hash as tokenHash, refresh_token_hash as refreshTokenHash,
             expires_at as expiresAt, created_at as createdAt, last_accessed as lastAccessed,
             user_agent as userAgent, ip_address as ipAddress
      FROM user_sessions
      WHERE refresh_token_hash = ? AND expires_at > NOW()
    `;
    return database.executeSingle<UserSession>(query, [refreshTokenHash]);
  }

  static async updateLastAccessed(id: number): Promise<void> {
    const query = 'UPDATE user_sessions SET last_accessed = CURRENT_TIMESTAMP WHERE id = ?';
    await database.execute(query, [id]);
  }

  static async updateTokens(id: number, accessToken: string, refreshToken: string, expiresAt: Date): Promise<void> {
    const tokenHash = AuthUtils.hashToken(accessToken);
    const refreshTokenHash = AuthUtils.hashToken(refreshToken);
    
    const query = `
      UPDATE user_sessions 
      SET token_hash = ?, refresh_token_hash = ?, expires_at = ?, last_accessed = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    await database.execute(query, [tokenHash, refreshTokenHash, expiresAt, id]);
  }

  static async deleteById(id: number): Promise<boolean> {
    const query = 'DELETE FROM user_sessions WHERE id = ?';
    const result = await database.execute(query, [id]);
    return (result as any).affectedRows > 0;
  }

  static async deleteByToken(token: string): Promise<boolean> {
    const tokenHash = AuthUtils.hashToken(token);
    const query = 'DELETE FROM user_sessions WHERE token_hash = ?';
    const result = await database.execute(query, [tokenHash]);
    return (result as any).affectedRows > 0;
  }

  static async deleteByUserId(userId: number): Promise<number> {
    const query = 'DELETE FROM user_sessions WHERE user_id = ?';
    const result = await database.execute(query, [userId]);
    return (result as any).affectedRows;
  }

  static async deleteExpiredSessions(): Promise<number> {
    const query = 'DELETE FROM user_sessions WHERE expires_at <= NOW()';
    const result = await database.execute(query);
    return (result as any).affectedRows;
  }

  static async getUserSessions(userId: number): Promise<UserSession[]> {
    const query = `
      SELECT id, user_id as userId, token_hash as tokenHash, refresh_token_hash as refreshTokenHash,
             expires_at as expiresAt, created_at as createdAt, last_accessed as lastAccessed,
             user_agent as userAgent, ip_address as ipAddress
      FROM user_sessions
      WHERE user_id = ? AND expires_at > NOW()
      ORDER BY last_accessed DESC
    `;
    return database.execute<UserSession>(query, [userId]);
  }

  static async getActiveSessionCount(userId: number): Promise<number> {
    const query = `
      SELECT COUNT(*) as count
      FROM user_sessions
      WHERE user_id = ? AND expires_at > NOW()
    `;
    const result = await database.executeSingle<{ count: number }>(query, [userId]);
    return result?.count || 0;
  }

  static async cleanupOldSessions(userId: number, maxSessions = 5): Promise<void> {
    const query = `
      DELETE FROM user_sessions
      WHERE user_id = ? AND id NOT IN (
        SELECT id FROM (
          SELECT id FROM user_sessions
          WHERE user_id = ? AND expires_at > NOW()
          ORDER BY last_accessed DESC
          LIMIT ?
        ) AS recent_sessions
      )
    `;
    
    await database.execute(query, [userId, userId, maxSessions]);
  }

  static async isValidSession(token: string): Promise<boolean> {
    const session = await this.findByToken(token);
    return session !== null;
  }

  static async refreshSession(refreshToken: string): Promise<{ accessToken: string; refreshToken: string; expiresAt: Date } | null> {
    const session = await this.findByRefreshToken(refreshToken);
    if (!session) {
      return null;
    }

    // 生成新的访问令牌和刷新令牌
    const payload = {
      userId: session.userId,
      username: '', // 这里需要从用户表获取
      email: ''     // 这里需要从用户表获取
    };

    // 获取用户信息
    const userQuery = `
      SELECT username, email FROM users WHERE id = ?
    `;
    const user = await database.executeSingle<{ username: string; email: string }>(userQuery, [session.userId]);
    
    if (!user) {
      return null;
    }

    payload.username = user.username;
    payload.email = user.email;

    const newAccessToken = AuthUtils.generateAccessToken(payload);
    const newRefreshToken = AuthUtils.generateRefreshToken(payload);
    const newExpiresAt = AuthUtils.getTokenExpiryDate(newAccessToken);

    if (!newExpiresAt) {
      throw new Error('Failed to get token expiry date');
    }

    // 更新会话
    await this.updateTokens(session.id, newAccessToken, newRefreshToken, newExpiresAt);

    return {
      accessToken: newAccessToken,
      refreshToken: newRefreshToken,
      expiresAt: newExpiresAt
    };
  }
}