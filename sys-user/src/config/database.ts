import mysql from 'mysql2/promise';

export interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  connectionLimit: number;
  acquireTimeout: number;
  timeout: number;
}

const config: DatabaseConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'user_management',
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '10'),
  acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),
  timeout: parseInt(process.env.DB_TIMEOUT || '60000')
};

export class Database {
  private pool: mysql.Pool;
  
  constructor() {
    this.pool = mysql.createPool({
      host: config.host,
      port: config.port,
      user: config.user,
      password: config.password,
      database: config.database,
      connectionLimit: config.connectionLimit,
      acquireTimeout: config.acquireTimeout,
      timeout: config.timeout,
      supportBigNumbers: true,
      bigNumberStrings: true,
      dateStrings: false,
      timezone: '+00:00'
    });
  }

  async execute<T = any>(query: string, params?: any[]): Promise<T[]> {
    try {
      const [rows] = await this.pool.execute(query, params);
      return rows as T[];
    } catch (error) {
      console.error('Database query error:', error);
      throw error;
    }
  }

  async executeSingle<T = any>(query: string, params?: any[]): Promise<T | null> {
    const results = await this.execute<T>(query, params);
    return results.length > 0 ? results[0] : null;
  }

  async beginTransaction(): Promise<mysql.PoolConnection> {
    const connection = await this.pool.getConnection();
    await connection.beginTransaction();
    return connection;
  }

  async commitTransaction(connection: mysql.PoolConnection): Promise<void> {
    await connection.commit();
    connection.release();
  }

  async rollbackTransaction(connection: mysql.PoolConnection): Promise<void> {
    await connection.rollback();
    connection.release();
  }

  async close(): Promise<void> {
    await this.pool.end();
  }

  getPool(): mysql.Pool {
    return this.pool;
  }
}

export const database = new Database();