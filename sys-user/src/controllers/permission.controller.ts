import { Request, Response } from 'express';
import { PermissionModel } from '../models/permission.model';
import { AuthorizationService } from '../services/authorization.service';

export class PermissionController {
  
  static async listPermissions(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      
      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(currentUser.id, 'permission', 'list');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const permissions = await PermissionModel.list();

      res.json({
        success: true,
        data: permissions
      });
    } catch (error) {
      console.error('List permissions error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async getResources(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      
      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(currentUser.id, 'permission', 'read');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const resources = await PermissionModel.getResources();

      res.json({
        success: true,
        data: resources
      });
    } catch (error) {
      console.error('Get resources error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async getActions(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      
      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(currentUser.id, 'permission', 'read');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const resource = req.query.resource as string;
      const actions = await PermissionModel.getActions(resource);

      res.json({
        success: true,
        data: actions
      });
    } catch (error) {
      console.error('Get actions error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async getPermissionsByResource(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      
      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(currentUser.id, 'permission', 'read');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const resource = req.params.resource;
      if (!resource) {
        res.status(400).json({
          success: false,
          message: 'Resource parameter is required'
        });
        return;
      }

      const permissions = await PermissionModel.findByResource(resource);

      res.json({
        success: true,
        data: permissions
      });
    } catch (error) {
      console.error('Get permissions by resource error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}