import { Request, Response } from 'express';
import { UserModel } from '../models/user.model';
import { AuthService } from '../services/auth.service';
import { AuthorizationService } from '../services/authorization.service';
import { AuditLogModel } from '../models/audit-log.model';
import {
  createUserSchema,
  updateUserSchema,
  loginSchema,
  changePasswordSchema,
  assignRolesSchema,
  refreshTokenSchema,
  listUsersSchema,
  userIdSchema
} from '../validators/user.validators';

export class UserController {
  
  static async register(req: Request, res: Response): Promise<void> {
    try {
      const { error, value } = createUserSchema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(detail => detail.message)
        });
        return;
      }

      // 检查用户名和邮箱是否已存在
      const exists = await UserModel.exists(value.username, value.email);
      if (exists) {
        res.status(409).json({
          success: false,
          message: 'Username or email already exists'
        });
        return;
      }

      // 创建用户
      const user = await UserModel.create(value);

      // 获取客户端IP和User-Agent
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');

      // 记录审计日志
      await AuditLogModel.logUserCreate(user.id, user.id, ipAddress, userAgent);

      res.status(201).json({
        success: true,
        message: 'User created successfully',
        data: user
      });
    } catch (error) {
      console.error('Register error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async login(req: Request, res: Response): Promise<void> {
    try {
      const { error, value } = loginSchema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(detail => detail.message)
        });
        return;
      }

      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');

      const authResult = await AuthService.login(value, ipAddress, userAgent);
      
      if (!authResult) {
        res.status(401).json({
          success: false,
          message: 'Invalid credentials'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Login successful',
        data: authResult
      });
    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async logout(req: Request, res: Response): Promise<void> {
    try {
      const token = req.headers.authorization?.replace('Bearer ', '');
      if (!token) {
        res.status(401).json({
          success: false,
          message: 'No token provided'
        });
        return;
      }

      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');

      const success = await AuthService.logout(token, ipAddress, userAgent);

      res.json({
        success: true,
        message: 'Logout successful'
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { error, value } = refreshTokenSchema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(detail => detail.message)
        });
        return;
      }

      const result = await AuthService.refreshToken(value.refreshToken);
      
      if (!result) {
        res.status(401).json({
          success: false,
          message: 'Invalid refresh token'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Token refreshed successfully',
        data: result
      });
    } catch (error) {
      console.error('Refresh token error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async getCurrentUser(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user;
      const permissions = await AuthorizationService.getUserPermissionsWithRoles(user.id);

      res.json({
        success: true,
        data: {
          user,
          permissions
        }
      });
    } catch (error) {
      console.error('Get current user error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async updateProfile(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user;
      const { error, value } = updateUserSchema.validate(req.body);
      
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(detail => detail.message)
        });
        return;
      }

      // 普通用户不能修改状态
      if (value.status !== undefined) {
        const canManage = await AuthorizationService.canUserManageUser(user.id, user.id);
        if (!canManage) {
          delete value.status;
        }
      }

      const updatedUser = await UserModel.update(user.id, value);

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: updatedUser
      });
    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async changePassword(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user;
      const { error, value } = changePasswordSchema.validate(req.body);
      
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(detail => detail.message)
        });
        return;
      }

      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');

      const success = await AuthService.changePassword(
        user.id,
        value.currentPassword,
        value.newPassword,
        ipAddress,
        userAgent
      );

      if (!success) {
        res.status(400).json({
          success: false,
          message: 'Current password is incorrect'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Password changed successfully'
      });
    } catch (error) {
      console.error('Change password error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async listUsers(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user;
      
      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(user.id, 'user', 'list');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const { error, value } = listUsersSchema.validate(req.query);
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(detail => detail.message)
        });
        return;
      }

      const { page, limit, status, search } = value;
      const offset = (page - 1) * limit;

      let users;
      let total;

      if (search) {
        users = await UserModel.search(search, offset, limit);
        total = (await UserModel.search(search, 0, 1000)).length; // 简化的总数计算
      } else {
        users = await UserModel.list(offset, limit, status);
        total = await UserModel.count(status);
      }

      res.json({
        success: true,
        data: {
          users,
          pagination: {
            page,
            limit,
            total,
            totalPages: Math.ceil(total / limit)
          }
        }
      });
    } catch (error) {
      console.error('List users error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async getUserById(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      const { error, value } = userIdSchema.validate(req.params);
      
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Invalid user ID'
        });
        return;
      }

      const { id } = value;

      // 检查权限
      const canManage = await AuthorizationService.canUserManageUser(currentUser.id, id);
      if (!canManage) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const user = await UserModel.findById(id);
      if (!user) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      // 如果是查看其他用户，获取其权限信息
      let permissions = null;
      if (id !== currentUser.id) {
        permissions = await AuthorizationService.getUserPermissionsWithRoles(id);
      }

      res.json({
        success: true,
        data: {
          user,
          permissions
        }
      });
    } catch (error) {
      console.error('Get user by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async updateUser(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      const { error: paramError, value: paramValue } = userIdSchema.validate(req.params);
      
      if (paramError) {
        res.status(400).json({
          success: false,
          message: 'Invalid user ID'
        });
        return;
      }

      const { error, value } = updateUserSchema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(detail => detail.message)
        });
        return;
      }

      const { id } = paramValue;

      // 检查权限
      const canManage = await AuthorizationService.canUserManageUser(currentUser.id, id);
      if (!canManage) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const updatedUser = await UserModel.update(id, value);
      if (!updatedUser) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'User updated successfully',
        data: updatedUser
      });
    } catch (error) {
      console.error('Update user error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async deleteUser(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      const { error, value } = userIdSchema.validate(req.params);
      
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Invalid user ID'
        });
        return;
      }

      const { id } = value;

      // 不能删除自己
      if (id === currentUser.id) {
        res.status(400).json({
          success: false,
          message: 'Cannot delete your own account'
        });
        return;
      }

      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(currentUser.id, 'user', 'delete');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const canManage = await AuthorizationService.canUserManageUser(currentUser.id, id);
      if (!canManage) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const success = await UserModel.delete(id);
      if (!success) {
        res.status(404).json({
          success: false,
          message: 'User not found'
        });
        return;
      }

      res.json({
        success: true,
        message: 'User deleted successfully'
      });
    } catch (error) {
      console.error('Delete user error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async assignRoles(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      const { error: paramError, value: paramValue } = userIdSchema.validate(req.params);
      
      if (paramError) {
        res.status(400).json({
          success: false,
          message: 'Invalid user ID'
        });
        return;
      }

      const { error, value } = assignRolesSchema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(detail => detail.message)
        });
        return;
      }

      const { id } = paramValue;
      const { roleIds } = value;

      // 检查权限
      const canManage = await AuthorizationService.canUserManageUser(currentUser.id, id);
      if (!canManage) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      // 分配角色
      await AuthorizationService.assignRoles(id, roleIds, currentUser.id);

      // 记录审计日志
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');

      for (const roleId of roleIds) {
        await AuditLogModel.logRoleAssign(currentUser.id, id, roleId, ipAddress, userAgent);
      }

      res.json({
        success: true,
        message: 'Roles assigned successfully'
      });
    } catch (error) {
      console.error('Assign roles error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async getUserSessions(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user;
      const sessions = await AuthService.getUserSessions(user.id);

      res.json({
        success: true,
        data: sessions
      });
    } catch (error) {
      console.error('Get user sessions error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async revokeSession(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user;
      const sessionId = parseInt(req.params.sessionId);

      if (isNaN(sessionId)) {
        res.status(400).json({
          success: false,
          message: 'Invalid session ID'
        });
        return;
      }

      const success = await AuthService.revokeSession(sessionId, user.id);
      if (!success) {
        res.status(404).json({
          success: false,
          message: 'Session not found or cannot be revoked'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Session revoked successfully'
      });
    } catch (error) {
      console.error('Revoke session error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}