import { Request, Response } from 'express';
import { RoleModel } from '../models/role.model';
import { PermissionModel } from '../models/permission.model';
import { AuthorizationService } from '../services/authorization.service';
import Joi from 'joi';

const createRoleSchema = Joi.object({
  name: Joi.string()
    .alphanum()
    .min(2)
    .max(50)
    .required()
    .messages({
      'string.alphanum': 'Role name must only contain alphanumeric characters',
      'string.min': 'Role name must be at least 2 characters long',
      'string.max': 'Role name must not exceed 50 characters',
      'any.required': 'Role name is required'
    }),
  
  displayName: Joi.string()
    .min(2)
    .max(100)
    .required()
    .messages({
      'string.min': 'Display name must be at least 2 characters long',
      'string.max': 'Display name must not exceed 100 characters',
      'any.required': 'Display name is required'
    }),
  
  description: Joi.string()
    .max(500)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Description must not exceed 500 characters'
    })
});

const updateRoleSchema = Joi.object({
  displayName: Joi.string()
    .min(2)
    .max(100)
    .optional()
    .messages({
      'string.min': 'Display name must be at least 2 characters long',
      'string.max': 'Display name must not exceed 100 characters'
    }),
  
  description: Joi.string()
    .max(500)
    .optional()
    .allow('')
    .messages({
      'string.max': 'Description must not exceed 500 characters'
    })
});

const assignPermissionsSchema = Joi.object({
  permissionIds: Joi.array()
    .items(Joi.number().integer().positive())
    .required()
    .messages({
      'any.required': 'Permission IDs are required'
    })
});

const roleIdSchema = Joi.object({
  id: Joi.number()
    .integer()
    .positive()
    .required()
    .messages({
      'number.positive': 'Role ID must be a positive number',
      'any.required': 'Role ID is required'
    })
});

export class RoleController {
  
  static async createRole(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      
      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(currentUser.id, 'role', 'create');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const { error, value } = createRoleSchema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(detail => detail.message)
        });
        return;
      }

      // 检查角色名是否已存在
      const exists = await RoleModel.exists(value.name);
      if (exists) {
        res.status(409).json({
          success: false,
          message: 'Role name already exists'
        });
        return;
      }

      const role = await RoleModel.create(value);

      res.status(201).json({
        success: true,
        message: 'Role created successfully',
        data: role
      });
    } catch (error) {
      console.error('Create role error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async listRoles(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      
      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(currentUser.id, 'role', 'list');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const roles = await RoleModel.list();

      res.json({
        success: true,
        data: roles
      });
    } catch (error) {
      console.error('List roles error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async getRoleById(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      const { error, value } = roleIdSchema.validate(req.params);
      
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Invalid role ID'
        });
        return;
      }

      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(currentUser.id, 'role', 'read');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const { id } = value;
      const role = await RoleModel.findById(id);
      
      if (!role) {
        res.status(404).json({
          success: false,
          message: 'Role not found'
        });
        return;
      }

      const permissions = await RoleModel.getPermissions(id);

      res.json({
        success: true,
        data: {
          role,
          permissions
        }
      });
    } catch (error) {
      console.error('Get role by ID error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async updateRole(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      const { error: paramError, value: paramValue } = roleIdSchema.validate(req.params);
      
      if (paramError) {
        res.status(400).json({
          success: false,
          message: 'Invalid role ID'
        });
        return;
      }

      const { error, value } = updateRoleSchema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(detail => detail.message)
        });
        return;
      }

      const { id } = paramValue;

      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(currentUser.id, 'role', 'update');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      // 检查是否可以管理该角色
      const canManage = await AuthorizationService.canUserManageRole(currentUser.id, id);
      if (!canManage) {
        res.status(403).json({
          success: false,
          message: 'Cannot modify system roles'
        });
        return;
      }

      const updatedRole = await RoleModel.update(id, value);
      if (!updatedRole) {
        res.status(404).json({
          success: false,
          message: 'Role not found or cannot be updated'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Role updated successfully',
        data: updatedRole
      });
    } catch (error) {
      console.error('Update role error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async deleteRole(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      const { error, value } = roleIdSchema.validate(req.params);
      
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Invalid role ID'
        });
        return;
      }

      const { id } = value;

      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(currentUser.id, 'role', 'delete');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      // 检查是否可以管理该角色
      const canManage = await AuthorizationService.canUserManageRole(currentUser.id, id);
      if (!canManage) {
        res.status(403).json({
          success: false,
          message: 'Cannot delete system roles'
        });
        return;
      }

      const success = await RoleModel.delete(id);
      if (!success) {
        res.status(404).json({
          success: false,
          message: 'Role not found or cannot be deleted'
        });
        return;
      }

      res.json({
        success: true,
        message: 'Role deleted successfully'
      });
    } catch (error) {
      console.error('Delete role error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async assignPermissions(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      const { error: paramError, value: paramValue } = roleIdSchema.validate(req.params);
      
      if (paramError) {
        res.status(400).json({
          success: false,
          message: 'Invalid role ID'
        });
        return;
      }

      const { error, value } = assignPermissionsSchema.validate(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Validation error',
          errors: error.details.map(detail => detail.message)
        });
        return;
      }

      const { id } = paramValue;
      const { permissionIds } = value;

      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(currentUser.id, 'role', 'update');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      // 检查是否可以管理该角色
      const canManage = await AuthorizationService.canUserManageRole(currentUser.id, id);
      if (!canManage) {
        res.status(403).json({
          success: false,
          message: 'Cannot modify system roles'
        });
        return;
      }

      // 验证所有权限ID是否存在
      for (const permissionId of permissionIds) {
        const permission = await PermissionModel.findById(permissionId);
        if (!permission) {
          res.status(400).json({
            success: false,
            message: `Permission with ID ${permissionId} not found`
          });
          return;
        }
      }

      await RoleModel.assignPermissions(id, permissionIds);

      res.json({
        success: true,
        message: 'Permissions assigned successfully'
      });
    } catch (error) {
      console.error('Assign permissions error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }

  static async getRolePermissions(req: Request, res: Response): Promise<void> {
    try {
      const currentUser = (req as any).user;
      const { error, value } = roleIdSchema.validate(req.params);
      
      if (error) {
        res.status(400).json({
          success: false,
          message: 'Invalid role ID'
        });
        return;
      }

      // 检查权限
      const hasPermission = await AuthorizationService.hasPermission(currentUser.id, 'role', 'read');
      if (!hasPermission) {
        res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
        return;
      }

      const { id } = value;
      const permissions = await RoleModel.getPermissions(id);

      res.json({
        success: true,
        data: permissions
      });
    } catch (error) {
      console.error('Get role permissions error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error'
      });
    }
  }
}