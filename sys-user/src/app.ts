import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import routes from './routes';
import { ErrorMiddleware } from './middleware/error.middleware';
import { RateLimitMiddleware } from './middleware/rate-limit.middleware';

const app = express();

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: false, // 如果需要可以自定义CSP
  crossOriginEmbedderPolicy: false
}));

// CORS配置
app.use(cors({
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

// 请求解析中间件
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 信任代理（如果在负载均衡器后面）
app.set('trust proxy', 1);

// 全局API限流
app.use(RateLimitMiddleware.limitAPI);

// 请求日志中间件（仅在开发环境）
if (process.env.NODE_ENV === 'development') {
  app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url} - ${req.ip}`);
    next();
  });
}

// API路由
app.use(routes);

// 404错误处理
app.use(ErrorMiddleware.notFound);

// 全局错误处理
app.use(ErrorMiddleware.handle);

export default app;