import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/auth.service';
import { AuthorizationService } from '../services/authorization.service';

export class AuthMiddleware {
  
  // 验证JWT令牌
  static async authenticate(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const authHeader = req.headers.authorization;
      
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          success: false,
          message: 'Access token required'
        });
        return;
      }

      const token = authHeader.substring(7); // 移除 'Bearer ' 前缀
      
      const user = await AuthService.validateToken(token);
      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Invalid or expired token'
        });
        return;
      }

      // 将用户信息附加到请求对象
      (req as any).user = user;
      (req as any).token = token;
      
      next();
    } catch (error) {
      console.error('Authentication error:', error);
      res.status(401).json({
        success: false,
        message: 'Authentication failed'
      });
    }
  }

  // 可选的认证中间件（令牌可有可无）
  static async optionalAuthenticate(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const authHeader = req.headers.authorization;
      
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const user = await AuthService.validateToken(token);
        
        if (user) {
          (req as any).user = user;
          (req as any).token = token;
        }
      }
      
      next();
    } catch (error) {
      // 可选认证失败时不阻止请求继续
      next();
    }
  }

  // 检查用户是否有特定权限
  static requirePermission(resource: string, action: string) {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const user = (req as any).user;
        
        if (!user) {
          res.status(401).json({
            success: false,
            message: 'Authentication required'
          });
          return;
        }

        const hasPermission = await AuthorizationService.hasPermission(user.id, resource, action);
        
        if (!hasPermission) {
          res.status(403).json({
            success: false,
            message: 'Insufficient permissions'
          });
          return;
        }

        next();
      } catch (error) {
        console.error('Permission check error:', error);
        res.status(500).json({
          success: false,
          message: 'Authorization check failed'
        });
      }
    };
  }

  // 检查用户是否有任一权限
  static requireAnyPermission(permissions: Array<{ resource: string; action: string }>) {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const user = (req as any).user;
        
        if (!user) {
          res.status(401).json({
            success: false,
            message: 'Authentication required'
          });
          return;
        }

        const hasAnyPermission = await AuthorizationService.hasAnyPermission(user.id, permissions);
        
        if (!hasAnyPermission) {
          res.status(403).json({
            success: false,
            message: 'Insufficient permissions'
          });
          return;
        }

        next();
      } catch (error) {
        console.error('Permission check error:', error);
        res.status(500).json({
          success: false,
          message: 'Authorization check failed'
        });
      }
    };
  }

  // 检查用户是否有特定角色
  static requireRole(roleName: string) {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const user = (req as any).user;
        
        if (!user) {
          res.status(401).json({
            success: false,
            message: 'Authentication required'
          });
          return;
        }

        const hasRole = await AuthorizationService.hasRole(user.id, roleName);
        
        if (!hasRole) {
          res.status(403).json({
            success: false,
            message: 'Insufficient role permissions'
          });
          return;
        }

        next();
      } catch (error) {
        console.error('Role check error:', error);
        res.status(500).json({
          success: false,
          message: 'Role authorization check failed'
        });
      }
    };
  }

  // 检查用户是否有任一角色
  static requireAnyRole(roleNames: string[]) {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const user = (req as any).user;
        
        if (!user) {
          res.status(401).json({
            success: false,
            message: 'Authentication required'
          });
          return;
        }

        const hasAnyRole = await AuthorizationService.hasAnyRole(user.id, roleNames);
        
        if (!hasAnyRole) {
          res.status(403).json({
            success: false,
            message: 'Insufficient role permissions'
          });
          return;
        }

        next();
      } catch (error) {
        console.error('Role check error:', error);
        res.status(500).json({
          success: false,
          message: 'Role authorization check failed'
        });
      }
    };
  }

  // 只允许管理员访问
  static requireAdmin() {
    return AuthMiddleware.requireAnyRole(['admin', 'super_admin']);
  }

  // 只允许超级管理员访问
  static requireSuperAdmin() {
    return AuthMiddleware.requireRole('super_admin');
  }

  // 检查用户是否只能访问自己的资源
  static requireSelfOrAdmin(userIdParam = 'id') {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const user = (req as any).user;
        
        if (!user) {
          res.status(401).json({
            success: false,
            message: 'Authentication required'
          });
          return;
        }

        const targetUserId = parseInt(req.params[userIdParam]);
        
        // 如果是访问自己的资源，直接通过
        if (user.id === targetUserId) {
          next();
          return;
        }

        // 否则检查是否是管理员
        const isAdmin = await AuthorizationService.hasAnyRole(user.id, ['admin', 'super_admin']);
        
        if (!isAdmin) {
          res.status(403).json({
            success: false,
            message: 'Access denied: can only access your own resources'
          });
          return;
        }

        next();
      } catch (error) {
        console.error('Self or admin check error:', error);
        res.status(500).json({
          success: false,
          message: 'Authorization check failed'
        });
      }
    };
  }

  // 限制管理员不能管理其他管理员（除非是超级管理员）
  static requireManagePermission() {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const user = (req as any).user;
        const targetUserId = parseInt(req.params.id);
        
        if (!user) {
          res.status(401).json({
            success: false,
            message: 'Authentication required'
          });
          return;
        }

        const canManage = await AuthorizationService.canUserManageUser(user.id, targetUserId);
        
        if (!canManage) {
          res.status(403).json({
            success: false,
            message: 'Insufficient permissions to manage this user'
          });
          return;
        }

        next();
      } catch (error) {
        console.error('Manage permission check error:', error);
        res.status(500).json({
          success: false,
          message: 'Authorization check failed'
        });
      }
    };
  }
}