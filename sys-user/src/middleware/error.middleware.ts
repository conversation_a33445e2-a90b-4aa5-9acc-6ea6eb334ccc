import { Request, Response, NextFunction } from 'express';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export class ErrorMiddleware {
  
  // 全局错误处理中间件
  static handle(error: AppError, req: Request, res: Response, next: NextFunction): void {
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      url: req.url,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    });

    // 默认错误状态码
    let statusCode = error.statusCode || 500;
    let message = error.message || 'Internal server error';
    let details: any = undefined;

    // 数据库错误处理
    if (error.message.includes('ER_DUP_ENTRY')) {
      statusCode = 409;
      message = 'Duplicate entry detected';
    } else if (error.message.includes('ER_NO_REFERENCED_ROW')) {
      statusCode = 400;
      message = 'Referenced record does not exist';
    } else if (error.message.includes('ER_ROW_IS_REFERENCED')) {
      statusCode = 400;
      message = 'Record is referenced by other records and cannot be deleted';
    } else if (error.message.includes('ER_BAD_FIELD_ERROR')) {
      statusCode = 400;
      message = 'Invalid field in query';
    } else if (error.message.includes('ER_PARSE_ERROR')) {
      statusCode = 400;
      message = 'Query syntax error';
    }

    // JWT错误处理
    if (error.message.includes('JsonWebTokenError')) {
      statusCode = 401;
      message = 'Invalid token';
    } else if (error.message.includes('TokenExpiredError')) {
      statusCode = 401;
      message = 'Token has expired';
    } else if (error.message.includes('NotBeforeError')) {
      statusCode = 401;
      message = 'Token not active';
    }

    // 验证错误处理
    if (error.message.includes('ValidationError')) {
      statusCode = 400;
      message = 'Validation failed';
    }

    // 网络错误处理
    if (error.message.includes('ECONNREFUSED')) {
      statusCode = 503;
      message = 'Service unavailable - database connection failed';
    } else if (error.message.includes('ETIMEDOUT')) {
      statusCode = 504;
      message = 'Request timeout';
    }

    // 文件系统错误处理
    if (error.message.includes('ENOENT')) {
      statusCode = 404;
      message = 'File not found';
    } else if (error.message.includes('EACCES')) {
      statusCode = 403;
      message = 'Permission denied';
    }

    // 开发环境下提供更多错误信息
    if (process.env.NODE_ENV === 'development') {
      details = {
        stack: error.stack,
        original: error.message
      };
    }

    const errorResponse: any = {
      success: false,
      message: message,
      error: {
        statusCode,
        timestamp: new Date().toISOString(),
        path: req.url,
        method: req.method
      }
    };

    if (details) {
      errorResponse.error.details = details;
    }

    res.status(statusCode).json(errorResponse);
  }

  // 404错误处理
  static notFound(req: Request, res: Response, next: NextFunction): void {
    const error: AppError = new Error(`Route ${req.originalUrl} not found`);
    error.statusCode = 404;
    error.isOperational = true;
    next(error);
  }

  // 创建自定义错误
  static createError(message: string, statusCode: number = 500): AppError {
    const error: AppError = new Error(message);
    error.statusCode = statusCode;
    error.isOperational = true;
    return error;
  }

  // 异步错误包装器
  static asyncHandler(fn: Function) {
    return (req: Request, res: Response, next: NextFunction): void => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  // 验证错误处理
  static validationError(message: string, errors?: any[]): AppError {
    const error: AppError = new Error(message);
    error.statusCode = 400;
    error.isOperational = true;
    (error as any).errors = errors;
    return error;
  }

  // 权限错误处理
  static permissionError(message: string = 'Insufficient permissions'): AppError {
    const error: AppError = new Error(message);
    error.statusCode = 403;
    error.isOperational = true;
    return error;
  }

  // 认证错误处理
  static authenticationError(message: string = 'Authentication required'): AppError {
    const error: AppError = new Error(message);
    error.statusCode = 401;
    error.isOperational = true;
    return error;
  }

  // 未找到资源错误
  static notFoundError(resource: string = 'Resource'): AppError {
    const error: AppError = new Error(`${resource} not found`);
    error.statusCode = 404;
    error.isOperational = true;
    return error;
  }

  // 冲突错误
  static conflictError(message: string): AppError {
    const error: AppError = new Error(message);
    error.statusCode = 409;
    error.isOperational = true;
    return error;
  }

  // 服务器错误
  static serverError(message: string = 'Internal server error'): AppError {
    const error: AppError = new Error(message);
    error.statusCode = 500;
    error.isOperational = true;
    return error;
  }
}

// 全局未捕获异常处理
process.on('uncaughtException', (error: Error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

// 全局未处理的Promise拒绝
process.on('unhandledRejection', (reason: any, promise: Promise<any>) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});