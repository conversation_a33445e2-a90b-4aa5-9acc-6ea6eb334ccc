import { Request, Response, NextFunction } from 'express';
import { RateLimiterMemory, RateLimiterRedis } from 'rate-limiter-flexible';

// 内存限流器配置
const loginLimiter = new RateLimiterMemory({
  keyPairs: ['ip', 'email'],
  points: 5, // 允许5次尝试
  duration: 900, // 15分钟窗口
  blockDuration: 900, // 阻止15分钟
});

const registerLimiter = new RateLimiterMemory({
  keyPairs: ['ip'],
  points: 3, // 允许3次注册
  duration: 3600, // 1小时窗口
  blockDuration: 3600, // 阻止1小时
});

const apiLimiter = new RateLimiterMemory({
  keyPairs: ['ip'],
  points: 100, // 允许100次请求
  duration: 60, // 1分钟窗口
  blockDuration: 60, // 阻止1分钟
});

const passwordChangeLimiter = new RateLimiterMemory({
  keyPairs: ['userId'],
  points: 3, // 允许3次密码修改
  duration: 3600, // 1小时窗口
  blockDuration: 3600, // 阻止1小时
});

export class RateLimitMiddleware {
  
  // 登录限流
  static async limitLogin(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const ip = req.ip || req.connection.remoteAddress || 'unknown';
      const email = req.body.usernameOrEmail || 'unknown';
      
      const resRateLimiter = await loginLimiter.consume(`${ip}_${email}`);
      
      // 添加限流信息到响应头
      res.set({
        'X-RateLimit-Limit': '5',
        'X-RateLimit-Remaining': resRateLimiter.remainingPoints?.toString() || '0',
        'X-RateLimit-Reset': new Date(Date.now() + resRateLimiter.msBeforeNext).toISOString(),
      });
      
      next();
    } catch (rejRes: any) {
      // 限流触发
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      
      res.set({
        'X-RateLimit-Limit': '5',
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': new Date(Date.now() + rejRes.msBeforeNext).toISOString(),
        'Retry-After': secs.toString(),
      });
      
      res.status(429).json({
        success: false,
        message: 'Too many login attempts. Please try again later.',
        retryAfter: secs
      });
    }
  }

  // 注册限流
  static async limitRegister(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const ip = req.ip || req.connection.remoteAddress || 'unknown';
      
      const resRateLimiter = await registerLimiter.consume(ip);
      
      res.set({
        'X-RateLimit-Limit': '3',
        'X-RateLimit-Remaining': resRateLimiter.remainingPoints?.toString() || '0',
        'X-RateLimit-Reset': new Date(Date.now() + resRateLimiter.msBeforeNext).toISOString(),
      });
      
      next();
    } catch (rejRes: any) {
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      
      res.set({
        'X-RateLimit-Limit': '3',
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': new Date(Date.now() + rejRes.msBeforeNext).toISOString(),
        'Retry-After': secs.toString(),
      });
      
      res.status(429).json({
        success: false,
        message: 'Too many registration attempts. Please try again later.',
        retryAfter: secs
      });
    }
  }

  // API通用限流
  static async limitAPI(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const ip = req.ip || req.connection.remoteAddress || 'unknown';
      
      const resRateLimiter = await apiLimiter.consume(ip);
      
      res.set({
        'X-RateLimit-Limit': '100',
        'X-RateLimit-Remaining': resRateLimiter.remainingPoints?.toString() || '0',
        'X-RateLimit-Reset': new Date(Date.now() + resRateLimiter.msBeforeNext).toISOString(),
      });
      
      next();
    } catch (rejRes: any) {
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      
      res.set({
        'X-RateLimit-Limit': '100',
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': new Date(Date.now() + rejRes.msBeforeNext).toISOString(),
        'Retry-After': secs.toString(),
      });
      
      res.status(429).json({
        success: false,
        message: 'Too many requests. Please try again later.',
        retryAfter: secs
      });
    }
  }

  // 密码修改限流
  static async limitPasswordChange(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const user = (req as any).user;
      if (!user) {
        next();
        return;
      }
      
      const resRateLimiter = await passwordChangeLimiter.consume(user.id.toString());
      
      res.set({
        'X-RateLimit-Limit': '3',
        'X-RateLimit-Remaining': resRateLimiter.remainingPoints?.toString() || '0',
        'X-RateLimit-Reset': new Date(Date.now() + resRateLimiter.msBeforeNext).toISOString(),
      });
      
      next();
    } catch (rejRes: any) {
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      
      res.set({
        'X-RateLimit-Limit': '3',
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': new Date(Date.now() + rejRes.msBeforeNext).toISOString(),
        'Retry-After': secs.toString(),
      });
      
      res.status(429).json({
        success: false,
        message: 'Too many password change attempts. Please try again later.',
        retryAfter: secs
      });
    }
  }

  // 创建自定义限流器
  static createCustomLimiter(points: number, duration: number, blockDuration?: number) {
    const limiter = new RateLimiterMemory({
      points,
      duration,
      blockDuration: blockDuration || duration,
    });

    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      try {
        const ip = req.ip || req.connection.remoteAddress || 'unknown';
        const resRateLimiter = await limiter.consume(ip);
        
        res.set({
          'X-RateLimit-Limit': points.toString(),
          'X-RateLimit-Remaining': resRateLimiter.remainingPoints?.toString() || '0',
          'X-RateLimit-Reset': new Date(Date.now() + resRateLimiter.msBeforeNext).toISOString(),
        });
        
        next();
      } catch (rejRes: any) {
        const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
        
        res.set({
          'X-RateLimit-Limit': points.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': new Date(Date.now() + rejRes.msBeforeNext).toISOString(),
          'Retry-After': secs.toString(),
        });
        
        res.status(429).json({
          success: false,
          message: 'Rate limit exceeded. Please try again later.',
          retryAfter: secs
        });
      }
    };
  }
}