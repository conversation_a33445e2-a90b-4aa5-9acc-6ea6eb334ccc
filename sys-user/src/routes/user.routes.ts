import { Router } from 'express';
import { UserController } from '../controllers/user.controller';
import { AuthMiddleware } from '../middleware/auth.middleware';
import { RateLimitMiddleware } from '../middleware/rate-limit.middleware';
import { ErrorMiddleware } from '../middleware/error.middleware';

const router = Router();

// 所有用户路由都需要认证
router.use(AuthMiddleware.authenticate);

// 获取用户列表 - 需要 user.list 权限
router.get('/', 
  AuthMiddleware.requirePermission('user', 'list'),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(UserController.listUsers)
);

// 获取指定用户信息 - 需要能够管理该用户
router.get('/:id', 
  AuthMiddleware.requireManagePermission(),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(UserController.getUserById)
);

// 更新指定用户信息 - 需要能够管理该用户
router.put('/:id', 
  AuthMiddleware.requireManagePermission(),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(UserController.updateUser)
);

// 删除用户 - 需要 user.delete 权限且能够管理该用户
router.delete('/:id', 
  AuthMiddleware.requirePermission('user', 'delete'),
  AuthMiddleware.requireManagePermission(),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(UserController.deleteUser)
);

// 分配角色给用户 - 需要能够管理该用户
router.post('/:id/roles', 
  AuthMiddleware.requireManagePermission(),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(UserController.assignRoles)
);

export default router;