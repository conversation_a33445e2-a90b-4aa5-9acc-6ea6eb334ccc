import { Router } from 'express';
import { PermissionController } from '../controllers/permission.controller';
import { AuthMiddleware } from '../middleware/auth.middleware';
import { RateLimitMiddleware } from '../middleware/rate-limit.middleware';
import { ErrorMiddleware } from '../middleware/error.middleware';

const router = Router();

// 所有权限路由都需要认证
router.use(AuthMiddleware.authenticate);

// 获取权限列表 - 需要 permission.list 权限
router.get('/', 
  AuthMiddleware.requirePermission('permission', 'list'),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(PermissionController.listPermissions)
);

// 获取资源列表 - 需要 permission.read 权限
router.get('/resources', 
  AuthMiddleware.requirePermission('permission', 'read'),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(PermissionController.getResources)
);

// 获取动作列表 - 需要 permission.read 权限
router.get('/actions', 
  AuthMiddleware.requirePermission('permission', 'read'),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(PermissionController.getActions)
);

// 根据资源获取权限列表 - 需要 permission.read 权限
router.get('/resource/:resource', 
  AuthMiddleware.requirePermission('permission', 'read'),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(PermissionController.getPermissionsByResource)
);

export default router;