import { Router } from 'express';
import { RoleController } from '../controllers/role.controller';
import { AuthMiddleware } from '../middleware/auth.middleware';
import { RateLimitMiddleware } from '../middleware/rate-limit.middleware';
import { ErrorMiddleware } from '../middleware/error.middleware';

const router = Router();

// 所有角色路由都需要认证
router.use(AuthMiddleware.authenticate);

// 获取角色列表 - 需要 role.list 权限
router.get('/', 
  AuthMiddleware.requirePermission('role', 'list'),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(RoleController.listRoles)
);

// 创建角色 - 需要 role.create 权限
router.post('/', 
  AuthMiddleware.requirePermission('role', 'create'),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(RoleController.createRole)
);

// 获取指定角色信息 - 需要 role.read 权限
router.get('/:id', 
  AuthMiddleware.requirePermission('role', 'read'),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(RoleController.getRoleById)
);

// 更新角色信息 - 需要 role.update 权限
router.put('/:id', 
  AuthMiddleware.requirePermission('role', 'update'),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(RoleController.updateRole)
);

// 删除角色 - 需要 role.delete 权限
router.delete('/:id', 
  AuthMiddleware.requirePermission('role', 'delete'),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(RoleController.deleteRole)
);

// 获取角色权限列表 - 需要 role.read 权限
router.get('/:id/permissions', 
  AuthMiddleware.requirePermission('role', 'read'),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(RoleController.getRolePermissions)
);

// 分配权限给角色 - 需要 role.update 权限
router.post('/:id/permissions', 
  AuthMiddleware.requirePermission('role', 'update'),
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(RoleController.assignPermissions)
);

export default router;