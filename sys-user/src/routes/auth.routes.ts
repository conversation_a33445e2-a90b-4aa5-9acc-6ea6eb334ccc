import { Router } from 'express';
import { UserController } from '../controllers/user.controller';
import { AuthMiddleware } from '../middleware/auth.middleware';
import { RateLimitMiddleware } from '../middleware/rate-limit.middleware';
import { ErrorMiddleware } from '../middleware/error.middleware';

const router = Router();

// 注册用户
router.post('/register', 
  RateLimitMiddleware.limitRegister,
  ErrorMiddleware.asyncHandler(UserController.register)
);

// 用户登录
router.post('/login', 
  RateLimitMiddleware.limitLogin,
  ErrorMiddleware.asyncHandler(UserController.login)
);

// 用户登出
router.post('/logout', 
  AuthMiddleware.authenticate,
  ErrorMiddleware.asyncHandler(UserController.logout)
);

// 刷新令牌
router.post('/refresh-token', 
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(UserController.refreshToken)
);

// 获取当前用户信息
router.get('/me', 
  AuthMiddleware.authenticate,
  ErrorMiddleware.asyncHandler(UserController.getCurrentUser)
);

// 更新用户资料
router.put('/profile', 
  AuthMiddleware.authenticate,
  RateLimitMiddleware.limitAPI,
  ErrorMiddleware.asyncHandler(UserController.updateProfile)
);

// 修改密码
router.put('/change-password', 
  AuthMiddleware.authenticate,
  RateLimitMiddleware.limitPasswordChange,
  ErrorMiddleware.asyncHandler(UserController.changePassword)
);

// 获取用户会话列表
router.get('/sessions', 
  AuthMiddleware.authenticate,
  ErrorMiddleware.asyncHandler(UserController.getUserSessions)
);

// 撤销指定会话
router.delete('/sessions/:sessionId', 
  AuthMiddleware.authenticate,
  ErrorMiddleware.asyncHandler(UserController.revokeSession)
);

export default router;