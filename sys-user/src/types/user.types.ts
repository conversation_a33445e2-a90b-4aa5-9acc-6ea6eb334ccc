export interface User {
  id: number;
  username: string;
  email: string;
  fullName?: string;
  phone?: string;
  avatarUrl?: string;
  status: 'active' | 'inactive' | 'suspended';
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserWithPassword extends User {
  passwordHash: string;
  salt: string;
}

export interface CreateUserInput {
  username: string;
  email: string;
  password: string;
  fullName?: string;
  phone?: string;
  avatarUrl?: string;
}

export interface UpdateUserInput {
  fullName?: string;
  phone?: string;
  avatarUrl?: string;
  status?: 'active' | 'inactive' | 'suspended';
}

export interface LoginInput {
  usernameOrEmail: string;
  password: string;
}

export interface AuthResult {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
}

export interface Role {
  id: number;
  name: string;
  displayName: string;
  description?: string;
  isSystem: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Permission {
  id: number;
  name: string;
  displayName: string;
  resource: string;
  action: string;
  description?: string;
  createdAt: Date;
}

export interface UserRole {
  id: number;
  userId: number;
  roleId: number;
  assignedBy?: number;
  assignedAt: Date;
  expiresAt?: Date;
}

export interface UserPermissions {
  userId: number;
  roles: Role[];
  permissions: Permission[];
}

export interface AuditLog {
  id: number;
  userId?: number;
  action: string;
  resourceType?: string;
  resourceId?: string;
  details?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: Date;
}