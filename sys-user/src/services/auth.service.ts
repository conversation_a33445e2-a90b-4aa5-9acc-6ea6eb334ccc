import { UserModel } from '../models/user.model';
import { SessionModel } from '../models/session.model';
import { AuditLogModel } from '../models/audit-log.model';
import { AuthUtils } from '../utils/auth.utils';
import { LoginInput, AuthResult, User } from '../types/user.types';

export class AuthService {
  static async login(input: LoginInput, ipAddress?: string, userAgent?: string): Promise<AuthResult | null> {
    // 验证用户凭据
    const user = await UserModel.validateCredentials(input);
    if (!user) {
      return null;
    }

    // 生成访问令牌和刷新令牌
    const accessToken = AuthUtils.generateAccessToken({
      userId: user.id,
      username: user.username,
      email: user.email
    });

    const refreshToken = AuthUtils.generateRefreshToken({
      userId: user.id,
      username: user.username,
      email: user.email
    });

    const expiresAt = AuthUtils.getTokenExpiryDate(accessToken);
    if (!expiresAt) {
      throw new Error('Failed to get token expiry date');
    }

    // 创建会话记录
    await SessionModel.create({
      userId: user.id,
      accessToken,
      refreshToken,
      expiresAt,
      userAgent,
      ipAddress
    });

    // 更新最后登录时间
    await UserModel.updateLastLogin(user.id);

    // 记录审计日志
    await AuditLogModel.logUserLogin(user.id, ipAddress, userAgent);

    // 清理旧会话（保留最近5个）
    await SessionModel.cleanupOldSessions(user.id, 5);

    // 移除密码相关字段
    const { passwordHash, salt, ...userWithoutPassword } = user;

    return {
      user: userWithoutPassword,
      accessToken,
      refreshToken,
      expiresAt
    };
  }

  static async logout(token: string, ipAddress?: string, userAgent?: string): Promise<boolean> {
    try {
      // 验证令牌并获取用户信息
      const payload = AuthUtils.verifyAccessToken(token);
      
      // 删除会话
      const deleted = await SessionModel.deleteByToken(token);
      
      if (deleted) {
        // 记录审计日志
        await AuditLogModel.logUserLogout(payload.userId, ipAddress, userAgent);
      }
      
      return deleted;
    } catch (error) {
      // 即使令牌无效，也尝试删除会话
      return await SessionModel.deleteByToken(token);
    }
  }

  static async logoutAll(userId: number, ipAddress?: string, userAgent?: string): Promise<number> {
    const deletedCount = await SessionModel.deleteByUserId(userId);
    
    if (deletedCount > 0) {
      // 记录审计日志
      await AuditLogModel.create({
        userId,
        action: 'user.logout_all',
        resourceType: 'user',
        resourceId: userId.toString(),
        details: { sessionCount: deletedCount },
        ipAddress,
        userAgent
      });
    }
    
    return deletedCount;
  }

  static async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string; expiresAt: Date } | null> {
    try {
      // 验证刷新令牌
      const payload = AuthUtils.verifyRefreshToken(refreshToken);
      
      // 使用会话模型的刷新方法
      const result = await SessionModel.refreshSession(refreshToken);
      
      return result;
    } catch (error) {
      return null;
    }
  }

  static async validateToken(token: string): Promise<User | null> {
    try {
      // 验证访问令牌
      const payload = AuthUtils.verifyAccessToken(token);
      
      // 检查会话是否存在且有效
      const session = await SessionModel.findByToken(token);
      if (!session) {
        return null;
      }

      // 更新最后访问时间
      await SessionModel.updateLastAccessed(session.id);

      // 获取用户信息
      const user = await UserModel.findById(payload.userId);
      if (!user || user.status !== 'active') {
        return null;
      }

      return user;
    } catch (error) {
      return null;
    }
  }

  static async getCurrentUser(token: string): Promise<User | null> {
    return this.validateToken(token);
  }

  static async changePassword(userId: number, currentPassword: string, newPassword: string, ipAddress?: string, userAgent?: string): Promise<boolean> {
    // 验证当前密码
    const user = await UserModel.findByUsernameOrEmail(''); // 需要通过ID获取用户
    const userById = await UserModel.findById(userId);
    if (!userById) {
      return false;
    }

    // 获取用户完整信息（包含密码）
    const userWithPassword = await UserModel.findByUsernameOrEmail(userById.username);
    if (!userWithPassword) {
      return false;
    }

    // 验证当前密码
    const isCurrentPasswordValid = await AuthUtils.verifyPassword(
      currentPassword,
      userWithPassword.passwordHash,
      userWithPassword.salt
    );

    if (!isCurrentPasswordValid) {
      return false;
    }

    // 更新密码
    const success = await UserModel.changePassword(userId, newPassword);
    
    if (success) {
      // 记录审计日志
      await AuditLogModel.create({
        userId,
        action: 'user.password_change',
        resourceType: 'user',
        resourceId: userId.toString(),
        ipAddress,
        userAgent
      });

      // 删除所有现有会话，强制重新登录
      await SessionModel.deleteByUserId(userId);
    }

    return success;
  }

  static async isTokenExpired(token: string): Promise<boolean> {
    return AuthUtils.isTokenExpired(token);
  }

  static async cleanupExpiredSessions(): Promise<number> {
    return SessionModel.deleteExpiredSessions();
  }

  static async getUserSessions(userId: number): Promise<Array<{
    id: number;
    createdAt: Date;
    lastAccessed: Date;
    userAgent?: string;
    ipAddress?: string;
    isCurrentSession?: boolean;
  }>> {
    const sessions = await SessionModel.getUserSessions(userId);
    
    return sessions.map(session => ({
      id: session.id,
      createdAt: session.createdAt,
      lastAccessed: session.lastAccessed,
      userAgent: session.userAgent,
      ipAddress: session.ipAddress
    }));
  }

  static async revokeSession(sessionId: number, currentUserId: number): Promise<boolean> {
    const session = await SessionModel.findById(sessionId);
    
    // 只允许用户撤销自己的会话
    if (!session || session.userId !== currentUserId) {
      return false;
    }

    return SessionModel.deleteById(sessionId);
  }
}