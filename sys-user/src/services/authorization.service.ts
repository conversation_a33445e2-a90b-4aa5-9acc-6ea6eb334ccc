import { database } from '../config/database';
import { User, Role, Permission, UserPermissions, UserRole } from '../types/user.types';

export class AuthorizationService {
  
  static async getUserRoles(userId: number): Promise<Role[]> {
    const query = `
      SELECT r.id, r.name, r.display_name as displayName, r.description, 
             r.is_system as isSystem, r.created_at as createdAt, r.updated_at as updatedAt
      FROM roles r
      JOIN user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ? AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
      ORDER BY r.name
    `;
    return database.execute<Role>(query, [userId]);
  }

  static async getUserPermissions(userId: number): Promise<Permission[]> {
    const query = `
      SELECT DISTINCT p.id, p.name, p.display_name as displayName, p.resource, 
             p.action, p.description, p.created_at as createdAt
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      JOIN user_roles ur ON rp.role_id = ur.role_id
      WHERE ur.user_id = ? AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
      ORDER BY p.resource, p.action
    `;
    return database.execute<Permission>(query, [userId]);
  }

  static async getUserPermissionsWithRoles(userId: number): Promise<UserPermissions> {
    const [roles, permissions] = await Promise.all([
      this.getUserRoles(userId),
      this.getUserPermissions(userId)
    ]);

    return {
      userId,
      roles,
      permissions
    };
  }

  static async hasPermission(userId: number, resource: string, action: string): Promise<boolean> {
    const query = `
      SELECT COUNT(*) as count
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      JOIN user_roles ur ON rp.role_id = ur.role_id
      WHERE ur.user_id = ? AND p.resource = ? AND p.action = ?
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    `;
    
    const result = await database.executeSingle<{ count: number }>(query, [userId, resource, action]);
    return (result?.count || 0) > 0;
  }

  static async hasRole(userId: number, roleName: string): Promise<boolean> {
    const query = `
      SELECT COUNT(*) as count
      FROM roles r
      JOIN user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ? AND r.name = ?
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    `;
    
    const result = await database.executeSingle<{ count: number }>(query, [userId, roleName]);
    return (result?.count || 0) > 0;
  }

  static async hasAnyRole(userId: number, roleNames: string[]): Promise<boolean> {
    if (roleNames.length === 0) return false;
    
    const placeholders = roleNames.map(() => '?').join(', ');
    const query = `
      SELECT COUNT(*) as count
      FROM roles r
      JOIN user_roles ur ON r.id = ur.role_id
      WHERE ur.user_id = ? AND r.name IN (${placeholders})
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    `;
    
    const result = await database.executeSingle<{ count: number }>(query, [userId, ...roleNames]);
    return (result?.count || 0) > 0;
  }

  static async hasAnyPermission(userId: number, permissions: Array<{ resource: string; action: string }>): Promise<boolean> {
    if (permissions.length === 0) return false;
    
    const conditions = permissions.map(() => '(p.resource = ? AND p.action = ?)').join(' OR ');
    const params = permissions.flatMap(p => [p.resource, p.action]);
    
    const query = `
      SELECT COUNT(*) as count
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      JOIN user_roles ur ON rp.role_id = ur.role_id
      WHERE ur.user_id = ? AND (${conditions})
        AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    `;
    
    const result = await database.executeSingle<{ count: number }>(query, [userId, ...params]);
    return (result?.count || 0) > 0;
  }

  static async assignRole(userId: number, roleId: number, assignedBy?: number, expiresAt?: Date): Promise<boolean> {
    const query = `
      INSERT INTO user_roles (user_id, role_id, assigned_by, expires_at)
      VALUES (?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE
        assigned_by = VALUES(assigned_by),
        expires_at = VALUES(expires_at),
        assigned_at = CURRENT_TIMESTAMP
    `;
    
    const result = await database.execute(query, [
      userId,
      roleId,
      assignedBy || null,
      expiresAt || null
    ]);
    
    return (result as any).affectedRows > 0;
  }

  static async revokeRole(userId: number, roleId: number): Promise<boolean> {
    const query = 'DELETE FROM user_roles WHERE user_id = ? AND role_id = ?';
    const result = await database.execute(query, [userId, roleId]);
    return (result as any).affectedRows > 0;
  }

  static async assignRoles(userId: number, roleIds: number[], assignedBy?: number): Promise<void> {
    if (roleIds.length === 0) return;
    
    const connection = await database.beginTransaction();
    
    try {
      // 删除现有角色（除了系统角色）
      await connection.execute(`
        DELETE ur FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = ? AND r.is_system = FALSE
      `, [userId]);
      
      // 分配新角色
      const values = roleIds.map(roleId => [userId, roleId, assignedBy || null, null]);
      const placeholders = values.map(() => '(?, ?, ?, ?)').join(', ');
      const flatValues = values.flat();
      
      if (flatValues.length > 0) {
        await connection.execute(
          `INSERT INTO user_roles (user_id, role_id, assigned_by, expires_at) VALUES ${placeholders}`,
          flatValues
        );
      }
      
      await database.commitTransaction(connection);
    } catch (error) {
      await database.rollbackTransaction(connection);
      throw error;
    }
  }

  static async getUserRoleAssignments(userId: number): Promise<UserRole[]> {
    const query = `
      SELECT ur.id, ur.user_id as userId, ur.role_id as roleId, 
             ur.assigned_by as assignedBy, ur.assigned_at as assignedAt, 
             ur.expires_at as expiresAt
      FROM user_roles ur
      WHERE ur.user_id = ?
      ORDER BY ur.assigned_at DESC
    `;
    return database.execute<UserRole>(query, [userId]);
  }

  static async isRoleAssigned(userId: number, roleId: number): Promise<boolean> {
    const query = `
      SELECT COUNT(*) as count
      FROM user_roles
      WHERE user_id = ? AND role_id = ?
        AND (expires_at IS NULL OR expires_at > NOW())
    `;
    
    const result = await database.executeSingle<{ count: number }>(query, [userId, roleId]);
    return (result?.count || 0) > 0;
  }

  static async canUserManageUser(managerId: number, targetUserId: number): Promise<boolean> {
    // 超级管理员可以管理所有用户
    if (await this.hasRole(managerId, 'super_admin')) {
      return true;
    }
    
    // 管理员可以管理普通用户，但不能管理其他管理员
    if (await this.hasRole(managerId, 'admin')) {
      const targetIsAdmin = await this.hasAnyRole(targetUserId, ['admin', 'super_admin']);
      return !targetIsAdmin;
    }
    
    // 普通用户只能管理自己
    return managerId === targetUserId;
  }

  static async canUserManageRole(userId: number, roleId: number): Promise<boolean> {
    // 超级管理员可以管理所有角色
    if (await this.hasRole(userId, 'super_admin')) {
      return true;
    }
    
    // 检查是否是系统角色
    const query = 'SELECT is_system FROM roles WHERE id = ?';
    const role = await database.executeSingle<{ is_system: boolean }>(query, [roleId]);
    
    // 只有超级管理员可以管理系统角色
    if (role?.is_system) {
      return false;
    }
    
    // 管理员可以管理非系统角色
    return this.hasRole(userId, 'admin');
  }

  static async cleanupExpiredRoles(): Promise<number> {
    const query = 'DELETE FROM user_roles WHERE expires_at IS NOT NULL AND expires_at <= NOW()';
    const result = await database.execute(query);
    return (result as any).affectedRows;
  }
}