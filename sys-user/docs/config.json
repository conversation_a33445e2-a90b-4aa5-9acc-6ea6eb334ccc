{"name": "用户管理系统文档", "version": "1.0.0", "description": "基于Node.js + TypeScript + MySQL的企业级用户管理系统文档", "author": "AI Agent", "repository": {"type": "git", "url": "https://github.com/your-org/user-management-system.git"}, "documentation": {"autoUpdate": true, "syncWithCode": true, "generateStats": true, "qualityCheck": true, "linkValidation": true}, "build": {"outputDir": "docs/dist", "theme": "default", "logo": "assets/logo.png", "favicon": "assets/favicon.ico"}, "navigation": [{"title": "快速开始", "path": "/getting-started"}, {"title": "API 文档", "path": "/api"}, {"title": "配置指南", "path": "/configuration"}, {"title": "部署指南", "path": "/deployment"}], "features": {"search": true, "darkMode": true, "editOnGithub": true, "lastUpdated": true, "contributors": true}, "integrations": {"github": {"enabled": true, "repo": "your-org/user-management-system", "branch": "main", "editPath": "docs"}, "analytics": {"enabled": false, "provider": "google", "trackingId": ""}}, "quality": {"minScore": 70, "checks": {"structure": true, "content": true, "links": true, "code": true, "language": true}, "notifications": {"slack": false, "email": false, "github": true}}}