# API 文档模板

## 接口名称

**接口描述**: [简要描述接口功能]

### 请求信息

- **请求方法**: `GET/POST/PUT/DELETE`
- **请求路径**: `/api/v1/endpoint`
- **认证要求**: `Bearer Token` / `无需认证`
- **权限要求**: `user.read` / `admin` / `无特殊权限`

### 请求参数

#### 路径参数
| 参数名 | 类型 | 必填 | 描述 | 示例 |
|--------|------|------|------|------|
| id | number | 是 | 用户ID | 123 |

#### 查询参数
| 参数名 | 类型 | 必填 | 描述 | 默认值 | 示例 |
|--------|------|------|------|-------|------|
| page | number | 否 | 页码 | 1 | 1 |
| limit | number | 否 | 每页数量 | 20 | 10 |

#### 请求体参数
| 参数名 | 类型 | 必填 | 描述 | 验证规则 | 示例 |
|--------|------|------|------|----------|------|
| username | string | 是 | 用户名 | 3-20字符 | "testuser" |
| email | string | 是 | 邮箱 | 有效邮箱格式 | "<EMAIL>" |

### 请求示例

```http
POST /api/v1/users
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "fullName": "Test User"
}
```

```javascript
// JavaScript 示例
const response = await fetch('/api/v1/users', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    username: 'testuser',
    email: '<EMAIL>',
    fullName: 'Test User'
  })
});

const data = await response.json();
```

```curl
# cURL 示例
curl -X POST \
  http://localhost:3000/api/v1/users \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...' \
  -H 'Content-Type: application/json' \
  -d '{
    "username": "testuser",
    "email": "<EMAIL>",
    "fullName": "Test User"
  }'
```

### 响应信息

#### 成功响应 (200)

```json
{
  "success": true,
  "message": "用户创建成功",
  "data": {
    "id": 123,
    "username": "testuser",
    "email": "<EMAIL>",
    "fullName": "Test User",
    "status": "active",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### 错误响应

**400 Bad Request** - 请求参数错误
```json
{
  "success": false,
  "message": "请求参数错误",
  "errors": [
    {
      "field": "email",
      "message": "邮箱格式不正确"
    }
  ]
}
```

**401 Unauthorized** - 未授权
```json
{
  "success": false,
  "message": "未授权访问",
  "code": "UNAUTHORIZED"
}
```

**403 Forbidden** - 权限不足
```json
{
  "success": false,
  "message": "权限不足",
  "code": "INSUFFICIENT_PERMISSIONS"
}
```

**404 Not Found** - 资源不存在
```json
{
  "success": false,
  "message": "用户不存在",
  "code": "USER_NOT_FOUND"
}
```

**500 Internal Server Error** - 服务器错误
```json
{
  "success": false,
  "message": "服务器内部错误",
  "code": "INTERNAL_ERROR"
}
```

### 响应字段说明

| 字段名 | 类型 | 描述 |
|--------|------|------|
| success | boolean | 请求是否成功 |
| message | string | 响应消息 |
| data | object | 响应数据 |
| errors | array | 错误详情（仅错误时） |

### 业务逻辑

1. 验证请求参数的有效性
2. 检查用户权限
3. 执行业务逻辑
4. 返回处理结果

### 注意事项

- 所有时间字段均为 ISO 8601 格式
- 分页查询最大限制为 100 条
- 敏感信息（如密码）不会在响应中返回
- 请求频率限制：每分钟最多 60 次请求

### 相关接口

- [获取用户列表](/api/users/list)
- [更新用户信息](/api/users/update)
- [删除用户](/api/users/delete)

### 更新日志

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| 1.0.0 | 2024-01-01 | 初始版本 |

---

*此文档由 API 文档生成工具自动生成，最后更新时间: 2024-01-01*
