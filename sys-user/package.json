{"name": "user-management-system", "version": "1.0.0", "description": "通用用户管理系统支持权限管理", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "typecheck": "tsc --noEmit", "docs:validate": "node scripts/validate-api-docs.js", "docs:stats": "node scripts/update-project-stats.js", "docs:quality": "node scripts/docs-quality-check.js", "docs:generate": "npm run docs:stats && npm run docs:quality", "docs:build": "echo 'Building documentation site...' && mkdir -p docs/dist && cp README.md docs/dist/index.md", "docs:check": "npm run docs:validate && npm run docs:quality", "precommit": "npm run lint && npm run typecheck && npm run docs:check"}, "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.1.0", "rate-limiter-flexible": "^3.0.8", "joi": "^17.11.0", "crypto": "^1.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.8.10", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/cors": "^2.8.17", "@types/joi": "^17.2.3", "@typescript-eslint/eslint-plugin": "^6.9.1", "@typescript-eslint/parser": "^6.9.1", "eslint": "^8.53.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2"}, "keywords": ["user-management", "rbac", "authentication", "authorization", "nodejs", "typescript"], "author": "AI Agent", "license": "MIT"}