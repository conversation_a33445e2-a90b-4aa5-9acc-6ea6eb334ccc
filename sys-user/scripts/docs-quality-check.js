#!/usr/bin/env node

/**
 * 📊 文档质量检查脚本
 * 分析文档的完整性、可读性、准确性等质量指标
 */

const fs = require('fs');
const path = require('path');

class DocsQualityChecker {
  constructor() {
    this.report = {
      score: 0,
      maxScore: 100,
      checks: [],
      suggestions: [],
      metrics: {}
    };
  }

  /**
   * 🚀 主检查流程
   */
  async checkQuality() {
    console.log('📊 开始文档质量检查...\n');
    
    try {
      await this.analyzeReadme();
      await this.checkCodeDocumentation();
      await this.validateStructure();
      await this.checkLanguageQuality();
      
      this.calculateScore();
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 质量检查过程中发生错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 📖 分析README文档
   */
  async analyzeReadme() {
    const readmePath = path.join(process.cwd(), 'README.md');
    
    if (!fs.existsSync(readmePath)) {
      this.addCheck('README存在性', false, 0, 10, 'README.md文件不存在');
      return;
    }

    const content = fs.readFileSync(readmePath, 'utf8');
    const lines = content.split('\n');
    
    // 检查基本结构
    this.checkReadmeStructure(content);
    
    // 检查内容质量
    this.checkContentQuality(content, lines);
    
    // 检查代码示例
    this.checkCodeExamples(content);
    
    this.report.metrics.readmeLength = content.length;
    this.report.metrics.readmeLines = lines.length;
  }

  /**
   * 🏗️  检查README结构
   */
  checkReadmeStructure(content) {
    const requiredSections = [
      { name: '项目标题', pattern: /^#\s+.+$/m, weight: 5 },
      { name: '项目描述', pattern: /基于|支持|系统/i, weight: 5 },
      { name: '安装说明', pattern: /安装|install/i, weight: 10 },
      { name: '使用说明', pattern: /使用|usage|快速开始/i, weight: 10 },
      { name: 'API文档', pattern: /api|接口/i, weight: 15 },
      { name: '配置说明', pattern: /配置|config/i, weight: 8 },
      { name: '贡献指南', pattern: /贡献|contribute/i, weight: 5 },
      { name: '许可证', pattern: /许可|license/i, weight: 3 }
    ];

    for (const section of requiredSections) {
      const exists = section.pattern.test(content);
      this.addCheck(
        `README包含${section.name}`,
        exists,
        exists ? section.weight : 0,
        section.weight,
        exists ? null : `缺少${section.name}部分`
      );
    }
  }

  /**
   * 📝 检查内容质量
   */
  checkContentQuality(content, lines) {
    // 检查文档长度
    const isGoodLength = content.length > 1000 && content.length < 20000;
    this.addCheck(
      '文档长度适中',
      isGoodLength,
      isGoodLength ? 5 : 0,
      5,
      isGoodLength ? null : `文档长度${content.length}字符，建议1000-20000字符`
    );

    // 检查标题层次
    const headers = content.match(/^#+\s+.+$/gm) || [];
    const hasGoodHierarchy = headers.length >= 5 && headers.length <= 20;
    this.addCheck(
      '标题层次合理',
      hasGoodHierarchy,
      hasGoodHierarchy ? 5 : 0,
      5,
      hasGoodHierarchy ? null : `标题数量${headers.length}，建议5-20个`
    );

    // 检查链接数量
    const links = content.match(/\[.*?\]\(.*?\)/g) || [];
    const hasLinks = links.length > 0;
    this.addCheck(
      '包含有用链接',
      hasLinks,
      hasLinks ? 3 : 0,
      3,
      hasLinks ? null : '建议添加相关链接'
    );

    // 检查表情符号使用
    const emojis = content.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu) || [];
    const hasEmojis = emojis.length > 0;
    this.addCheck(
      '使用表情符号增强可读性',
      hasEmojis,
      hasEmojis ? 2 : 0,
      2,
      hasEmojis ? null : '建议适当使用表情符号'
    );
  }

  /**
   * 💻 检查代码示例
   */
  checkCodeExamples(content) {
    const codeBlocks = content.match(/```[\s\S]*?```/g) || [];
    const hasCodeExamples = codeBlocks.length > 0;
    
    this.addCheck(
      '包含代码示例',
      hasCodeExamples,
      hasCodeExamples ? 10 : 0,
      10,
      hasCodeExamples ? null : '建议添加代码使用示例'
    );

    if (hasCodeExamples) {
      // 检查代码块语言标识
      const languageBlocks = codeBlocks.filter(block => 
        /```\w+/.test(block)
      );
      const hasLanguageIds = languageBlocks.length > 0;
      
      this.addCheck(
        '代码块有语言标识',
        hasLanguageIds,
        hasLanguageIds ? 3 : 0,
        3,
        hasLanguageIds ? null : '建议为代码块添加语言标识'
      );
    }

    this.report.metrics.codeBlocks = codeBlocks.length;
  }

  /**
   * 📚 检查代码文档
   */
  async checkCodeDocumentation() {
    const srcDir = path.join(process.cwd(), 'src');
    
    if (!fs.existsSync(srcDir)) {
      this.addCheck('源码目录存在', false, 0, 5, 'src目录不存在');
      return;
    }

    let totalFiles = 0;
    let documentedFiles = 0;
    
    this.scanDirectory(srcDir, (filePath) => {
      if (filePath.endsWith('.ts') || filePath.endsWith('.js')) {
        totalFiles++;
        
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 检查是否有JSDoc注释
        const hasJSDoc = /\/\*\*[\s\S]*?\*\//.test(content);
        // 检查是否有行注释
        const hasComments = /\/\/.*/.test(content);
        
        if (hasJSDoc || hasComments) {
          documentedFiles++;
        }
      }
    });

    const documentationRatio = totalFiles > 0 ? documentedFiles / totalFiles : 0;
    const isWellDocumented = documentationRatio >= 0.7;
    
    this.addCheck(
      '代码文档覆盖率',
      isWellDocumented,
      Math.round(documentationRatio * 10),
      10,
      isWellDocumented ? null : `代码文档覆盖率${Math.round(documentationRatio * 100)}%，建议≥70%`
    );

    this.report.metrics.totalFiles = totalFiles;
    this.report.metrics.documentedFiles = documentedFiles;
    this.report.metrics.documentationRatio = documentationRatio;
  }

  /**
   * 🏗️  验证项目结构
   */
  async validateStructure() {
    const expectedFiles = [
      { path: 'package.json', weight: 5 },
      { path: 'tsconfig.json', weight: 3 },
      { path: '.gitignore', weight: 2 },
      { path: 'src', weight: 10 },
      { path: 'src/index.ts', weight: 5 }
    ];

    for (const file of expectedFiles) {
      const exists = fs.existsSync(path.join(process.cwd(), file.path));
      this.addCheck(
        `${file.path}存在`,
        exists,
        exists ? file.weight : 0,
        file.weight,
        exists ? null : `缺少${file.path}文件`
      );
    }
  }

  /**
   * 🔤 检查语言质量
   */
  async checkLanguageQuality() {
    const readmePath = path.join(process.cwd(), 'README.md');
    
    if (!fs.existsSync(readmePath)) return;
    
    const content = fs.readFileSync(readmePath, 'utf8');
    
    // 检查中英文混排
    const hasMixedLanguage = /[a-zA-Z].*[\u4e00-\u9fa5]|[\u4e00-\u9fa5].*[a-zA-Z]/.test(content);
    this.addCheck(
      '支持中英文混排',
      hasMixedLanguage,
      hasMixedLanguage ? 3 : 0,
      3,
      hasMixedLanguage ? null : '建议支持中英文混排以提高国际化'
    );

    // 检查专业术语一致性
    const terms = ['API', 'JWT', 'RBAC', 'MySQL', 'TypeScript'];
    let consistentTerms = 0;
    
    for (const term of terms) {
      const regex = new RegExp(term, 'g');
      const matches = content.match(regex) || [];
      if (matches.length > 0) {
        consistentTerms++;
      }
    }
    
    const hasConsistentTerms = consistentTerms >= 3;
    this.addCheck(
      '专业术语使用一致',
      hasConsistentTerms,
      hasConsistentTerms ? 3 : 0,
      3,
      hasConsistentTerms ? null : '建议统一专业术语的使用'
    );
  }

  /**
   * 📁 递归扫描目录
   */
  scanDirectory(dirPath, callback) {
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        this.scanDirectory(itemPath, callback);
      } else if (stat.isFile()) {
        callback(itemPath);
      }
    }
  }

  /**
   * ✅ 添加检查项
   */
  addCheck(name, passed, score, maxScore, suggestion) {
    this.report.checks.push({
      name,
      passed,
      score,
      maxScore,
      suggestion
    });
    
    if (suggestion) {
      this.report.suggestions.push(suggestion);
    }
  }

  /**
   * 🧮 计算总分
   */
  calculateScore() {
    const totalScore = this.report.checks.reduce((sum, check) => sum + check.score, 0);
    const totalMaxScore = this.report.checks.reduce((sum, check) => sum + check.maxScore, 0);
    
    this.report.score = totalScore;
    this.report.maxScore = totalMaxScore;
  }

  /**
   * 📊 生成质量报告
   */
  generateReport() {
    const percentage = Math.round((this.report.score / this.report.maxScore) * 100);
    
    console.log(`📊 文档质量评分: ${this.report.score}/${this.report.maxScore} (${percentage}%)\n`);
    
    // 生成Markdown报告
    let markdownReport = `### 📊 文档质量报告\n\n`;
    markdownReport += `**总评分**: ${this.report.score}/${this.report.maxScore} (${percentage}%)\n\n`;
    
    // 评级
    let grade = 'F';
    let emoji = '❌';
    if (percentage >= 90) { grade = 'A+'; emoji = '🏆'; }
    else if (percentage >= 80) { grade = 'A'; emoji = '✅'; }
    else if (percentage >= 70) { grade = 'B'; emoji = '👍'; }
    else if (percentage >= 60) { grade = 'C'; emoji = '⚠️'; }
    else if (percentage >= 50) { grade = 'D'; emoji = '🔶'; }
    
    markdownReport += `**质量等级**: ${emoji} ${grade}\n\n`;
    
    // 检查项详情
    markdownReport += `#### 📋 检查项详情\n\n`;
    markdownReport += `| 检查项 | 状态 | 得分 | 满分 |\n`;
    markdownReport += `|--------|------|------|------|\n`;
    
    for (const check of this.report.checks) {
      const status = check.passed ? '✅' : '❌';
      markdownReport += `| ${check.name} | ${status} | ${check.score} | ${check.maxScore} |\n`;
    }
    
    // 改进建议
    if (this.report.suggestions.length > 0) {
      markdownReport += `\n#### 💡 改进建议\n\n`;
      for (const suggestion of this.report.suggestions) {
        markdownReport += `- ${suggestion}\n`;
      }
    }
    
    // 项目指标
    if (Object.keys(this.report.metrics).length > 0) {
      markdownReport += `\n#### 📈 项目指标\n\n`;
      for (const [key, value] of Object.entries(this.report.metrics)) {
        markdownReport += `- **${key}**: ${value}\n`;
      }
    }
    
    // 保存报告
    fs.writeFileSync('docs-quality-report.md', markdownReport);
    
    console.log('✅ 质量报告已生成: docs-quality-report.md');
    console.log(`🎯 质量等级: ${emoji} ${grade} (${percentage}%)`);
    
    if (this.report.suggestions.length > 0) {
      console.log('\n💡 主要改进建议:');
      this.report.suggestions.slice(0, 3).forEach(suggestion => {
        console.log(`  - ${suggestion}`);
      });
    }
  }
}

// 运行质量检查
if (require.main === module) {
  const checker = new DocsQualityChecker();
  checker.checkQuality().catch(error => {
    console.error('💥 质量检查失败:', error);
    process.exit(1);
  });
}

module.exports = DocsQualityChecker;
