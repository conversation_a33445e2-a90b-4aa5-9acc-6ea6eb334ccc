#!/usr/bin/env node

/**
 * 📊 项目统计信息更新脚本
 * 自动统计代码行数、文件数量、测试覆盖率等信息并更新到README
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ProjectStatsUpdater {
  constructor() {
    this.stats = {
      totalFiles: 0,
      totalLines: 0,
      codeLines: 0,
      testFiles: 0,
      testCoverage: 0,
      dependencies: 0,
      devDependencies: 0,
      lastCommit: '',
      contributors: 0
    };
  }

  /**
   * 🚀 主更新流程
   */
  async updateStats() {
    console.log('📊 开始更新项目统计信息...\n');
    
    try {
      await this.collectStats();
      await this.updateReadme();
      console.log('✅ 项目统计信息更新完成！');
    } catch (error) {
      console.error('❌ 更新统计信息时发生错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 📈 收集项目统计信息
   */
  async collectStats() {
    console.log('📈 收集项目统计信息...');
    
    // 统计文件和代码行数
    await this.countFilesAndLines();
    
    // 统计依赖
    await this.countDependencies();
    
    // 获取Git信息
    await this.getGitInfo();
    
    // 获取测试覆盖率
    await this.getTestCoverage();
    
    console.log('📊 统计信息收集完成');
  }

  /**
   * 📁 统计文件和代码行数
   */
  async countFilesAndLines() {
    const srcDir = path.join(process.cwd(), 'src');
    const testDir = path.join(process.cwd(), 'test');
    const testsDir = path.join(process.cwd(), 'tests');
    
    if (fs.existsSync(srcDir)) {
      const srcStats = this.countDirectory(srcDir, ['.ts', '.js']);
      this.stats.totalFiles += srcStats.files;
      this.stats.totalLines += srcStats.lines;
      this.stats.codeLines += srcStats.codeLines;
    }
    
    // 统计测试文件
    for (const dir of [testDir, testsDir]) {
      if (fs.existsSync(dir)) {
        const testStats = this.countDirectory(dir, ['.test.ts', '.test.js', '.spec.ts', '.spec.js']);
        this.stats.testFiles += testStats.files;
      }
    }
  }

  /**
   * 📂 递归统计目录
   */
  countDirectory(dirPath, extensions) {
    let files = 0;
    let lines = 0;
    let codeLines = 0;
    
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        const subStats = this.countDirectory(itemPath, extensions);
        files += subStats.files;
        lines += subStats.lines;
        codeLines += subStats.codeLines;
      } else if (stat.isFile()) {
        const ext = path.extname(item);
        const hasValidExt = extensions.some(validExt => 
          item.endsWith(validExt) || ext === validExt
        );
        
        if (hasValidExt) {
          files++;
          const content = fs.readFileSync(itemPath, 'utf8');
          const fileLines = content.split('\n');
          lines += fileLines.length;
          
          // 统计非空非注释行
          const nonEmptyLines = fileLines.filter(line => {
            const trimmed = line.trim();
            return trimmed && 
                   !trimmed.startsWith('//') && 
                   !trimmed.startsWith('/*') && 
                   !trimmed.startsWith('*') &&
                   !trimmed.startsWith('*/');
          });
          codeLines += nonEmptyLines.length;
        }
      }
    }
    
    return { files, lines, codeLines };
  }

  /**
   * 📦 统计依赖数量
   */
  async countDependencies() {
    const packagePath = path.join(process.cwd(), 'package.json');
    
    if (fs.existsSync(packagePath)) {
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      this.stats.dependencies = Object.keys(packageJson.dependencies || {}).length;
      this.stats.devDependencies = Object.keys(packageJson.devDependencies || {}).length;
    }
  }

  /**
   * 🔄 获取Git信息
   */
  async getGitInfo() {
    try {
      // 最后提交信息
      this.stats.lastCommit = execSync('git log -1 --format="%h %s"', { encoding: 'utf8' }).trim();
      
      // 贡献者数量
      const contributors = execSync('git shortlog -sn --all', { encoding: 'utf8' });
      this.stats.contributors = contributors.split('\n').filter(line => line.trim()).length;
    } catch (error) {
      console.log('⚠️  无法获取Git信息:', error.message);
    }
  }

  /**
   * 🧪 获取测试覆盖率
   */
  async getTestCoverage() {
    try {
      // 尝试运行测试覆盖率命令
      const coverage = execSync('npm run test:coverage 2>/dev/null || npm test -- --coverage 2>/dev/null || echo "0"', { encoding: 'utf8' });
      
      // 简单解析覆盖率 (实际项目可能需要更复杂的解析)
      const coverageMatch = coverage.match(/(\d+(?:\.\d+)?)%/);
      if (coverageMatch) {
        this.stats.testCoverage = parseFloat(coverageMatch[1]);
      }
    } catch (error) {
      console.log('⚠️  无法获取测试覆盖率:', error.message);
    }
  }

  /**
   * 📝 更新README文件
   */
  async updateReadme() {
    const readmePath = path.join(process.cwd(), 'README.md');
    
    if (!fs.existsSync(readmePath)) {
      console.log('⚠️  README.md 文件不存在，跳过更新');
      return;
    }

    let content = fs.readFileSync(readmePath, 'utf8');
    
    // 生成统计信息徽章
    const statsSection = this.generateStatsSection();
    
    // 查找并替换统计信息部分
    const statsRegex = /<!-- PROJECT_STATS_START -->[\s\S]*?<!-- PROJECT_STATS_END -->/;
    
    if (statsRegex.test(content)) {
      content = content.replace(statsRegex, statsSection);
    } else {
      // 如果没有找到统计信息部分，在文档开头添加
      const titleMatch = content.match(/^#\s+.+$/m);
      if (titleMatch) {
        const insertIndex = content.indexOf(titleMatch[0]) + titleMatch[0].length;
        content = content.slice(0, insertIndex) + '\n\n' + statsSection + '\n' + content.slice(insertIndex);
      }
    }
    
    fs.writeFileSync(readmePath, content);
    console.log('📝 README.md 统计信息已更新');
  }

  /**
   * 🏷️  生成统计信息部分
   */
  generateStatsSection() {
    const { stats } = this;
    
    return `<!-- PROJECT_STATS_START -->
## 📊 项目统计

![代码行数](https://img.shields.io/badge/代码行数-${stats.codeLines}-blue)
![文件数量](https://img.shields.io/badge/文件数量-${stats.totalFiles}-green)
![测试文件](https://img.shields.io/badge/测试文件-${stats.testFiles}-yellow)
![测试覆盖率](https://img.shields.io/badge/测试覆盖率-${stats.testCoverage}%25-brightgreen)
![依赖数量](https://img.shields.io/badge/依赖-${stats.dependencies}-orange)
![开发依赖](https://img.shields.io/badge/开发依赖-${stats.devDependencies}-lightgrey)
![贡献者](https://img.shields.io/badge/贡献者-${stats.contributors}-purple)

| 指标 | 数值 |
|------|------|
| 📁 总文件数 | ${stats.totalFiles} |
| 📝 总行数 | ${stats.totalLines.toLocaleString()} |
| 💻 代码行数 | ${stats.codeLines.toLocaleString()} |
| 🧪 测试文件 | ${stats.testFiles} |
| 📊 测试覆盖率 | ${stats.testCoverage}% |
| 📦 生产依赖 | ${stats.dependencies} |
| 🔧 开发依赖 | ${stats.devDependencies} |
| 👥 贡献者 | ${stats.contributors} |
| 🔄 最后提交 | ${stats.lastCommit} |

*统计信息自动更新于: ${new Date().toLocaleString('zh-CN')}*
<!-- PROJECT_STATS_END -->`;
  }
}

// 运行更新
if (require.main === module) {
  const updater = new ProjectStatsUpdater();
  updater.updateStats().catch(error => {
    console.error('💥 更新失败:', error);
    process.exit(1);
  });
}

module.exports = ProjectStatsUpdater;
