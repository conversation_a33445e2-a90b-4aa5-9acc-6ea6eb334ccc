#!/usr/bin/env node

/**
 * 📊 文档同步状态监控脚本
 * 监控文档与代码的同步状态，生成同步报告
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class DocsSyncMonitor {
  constructor() {
    this.syncStatus = {
      lastCodeChange: null,
      lastDocsUpdate: null,
      syncLag: 0,
      outdatedSections: [],
      missingDocs: [],
      status: 'unknown'
    };
  }

  /**
   * 🚀 主监控流程
   */
  async monitor() {
    console.log('📊 开始监控文档同步状态...\n');
    
    try {
      await this.checkGitHistory();
      await this.analyzeCodeChanges();
      await this.checkDocumentationCoverage();
      await this.generateSyncReport();
      
    } catch (error) {
      console.error('❌ 监控过程中发生错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 📅 检查Git历史
   */
  async checkGitHistory() {
    try {
      // 获取最后一次代码变更
      const lastCodeCommit = execSync(
        'git log -1 --format="%H|%ct|%s" --grep="^(?!.*docs).*" -- src/',
        { encoding: 'utf8' }
      ).trim();
      
      if (lastCodeCommit) {
        const [hash, timestamp, message] = lastCodeCommit.split('|');
        this.syncStatus.lastCodeChange = {
          hash,
          timestamp: parseInt(timestamp),
          message,
          date: new Date(parseInt(timestamp) * 1000)
        };
      }

      // 获取最后一次文档更新
      const lastDocsCommit = execSync(
        'git log -1 --format="%H|%ct|%s" -- README.md docs/',
        { encoding: 'utf8' }
      ).trim();
      
      if (lastDocsCommit) {
        const [hash, timestamp, message] = lastDocsCommit.split('|');
        this.syncStatus.lastDocsUpdate = {
          hash,
          timestamp: parseInt(timestamp),
          message,
          date: new Date(parseInt(timestamp) * 1000)
        };
      }

      // 计算同步延迟
      if (this.syncStatus.lastCodeChange && this.syncStatus.lastDocsUpdate) {
        this.syncStatus.syncLag = this.syncStatus.lastCodeChange.timestamp - this.syncStatus.lastDocsUpdate.timestamp;
      }

    } catch (error) {
      console.log('⚠️  无法获取Git历史:', error.message);
    }
  }

  /**
   * 🔍 分析代码变更
   */
  async analyzeCodeChanges() {
    try {
      // 获取最近的代码变更文件
      const changedFiles = execSync(
        'git diff --name-only HEAD~5 HEAD -- src/',
        { encoding: 'utf8' }
      ).trim().split('\n').filter(f => f);

      // 分析变更类型
      const apiChanges = changedFiles.filter(f => 
        f.includes('routes/') || f.includes('controllers/')
      );
      
      const modelChanges = changedFiles.filter(f => 
        f.includes('models/') || f.includes('types/')
      );

      if (apiChanges.length > 0) {
        this.syncStatus.outdatedSections.push({
          section: 'API文档',
          reason: `检测到 ${apiChanges.length} 个API相关文件变更`,
          files: apiChanges
        });
      }

      if (modelChanges.length > 0) {
        this.syncStatus.outdatedSections.push({
          section: '数据模型文档',
          reason: `检测到 ${modelChanges.length} 个模型文件变更`,
          files: modelChanges
        });
      }

    } catch (error) {
      console.log('⚠️  无法分析代码变更:', error.message);
    }
  }

  /**
   * 📚 检查文档覆盖率
   */
  async checkDocumentationCoverage() {
    const srcDir = path.join(process.cwd(), 'src');
    const readmePath = path.join(process.cwd(), 'README.md');
    
    if (!fs.existsSync(srcDir) || !fs.existsSync(readmePath)) {
      return;
    }

    const readmeContent = fs.readFileSync(readmePath, 'utf8');
    
    // 检查路由文件是否在文档中有对应说明
    const routesDir = path.join(srcDir, 'routes');
    if (fs.existsSync(routesDir)) {
      const routeFiles = fs.readdirSync(routesDir)
        .filter(f => f.endsWith('.ts') || f.endsWith('.js'));
      
      for (const routeFile of routeFiles) {
        const routeName = path.basename(routeFile, path.extname(routeFile));
        const hasDocumentation = readmeContent.includes(routeName) || 
                                readmeContent.includes(routeName.replace('.routes', ''));
        
        if (!hasDocumentation) {
          this.syncStatus.missingDocs.push({
            type: 'route',
            file: routeFile,
            suggestion: `为 ${routeName} 路由添加API文档`
          });
        }
      }
    }

    // 检查模型文件是否在文档中有说明
    const modelsDir = path.join(srcDir, 'models');
    if (fs.existsSync(modelsDir)) {
      const modelFiles = fs.readdirSync(modelsDir)
        .filter(f => f.endsWith('.ts') || f.endsWith('.js'));
      
      for (const modelFile of modelFiles) {
        const modelName = path.basename(modelFile, path.extname(modelFile));
        const hasDocumentation = readmeContent.includes(modelName) || 
                                readmeContent.includes(modelName.replace('.model', ''));
        
        if (!hasDocumentation) {
          this.syncStatus.missingDocs.push({
            type: 'model',
            file: modelFile,
            suggestion: `为 ${modelName} 模型添加数据结构说明`
          });
        }
      }
    }
  }

  /**
   * 📊 生成同步报告
   */
  async generateSyncReport() {
    // 确定同步状态
    if (this.syncStatus.syncLag === 0) {
      this.syncStatus.status = 'synced';
    } else if (this.syncStatus.syncLag > 0 && this.syncStatus.syncLag < 86400) { // 1天
      this.syncStatus.status = 'slightly-outdated';
    } else if (this.syncStatus.syncLag >= 86400) {
      this.syncStatus.status = 'outdated';
    }

    // 生成控制台报告
    this.printConsoleReport();
    
    // 生成Markdown报告
    this.generateMarkdownReport();
    
    // 更新README状态徽章
    this.updateStatusBadge();
  }

  /**
   * 🖥️  打印控制台报告
   */
  printConsoleReport() {
    console.log('📊 文档同步状态报告');
    console.log('='.repeat(50));
    
    const statusEmoji = {
      'synced': '🟢',
      'slightly-outdated': '🟡',
      'outdated': '🔴',
      'unknown': '⚪'
    };
    
    console.log(`状态: ${statusEmoji[this.syncStatus.status]} ${this.syncStatus.status.toUpperCase()}`);
    
    if (this.syncStatus.lastCodeChange) {
      console.log(`最后代码变更: ${this.syncStatus.lastCodeChange.date.toLocaleString()}`);
      console.log(`变更内容: ${this.syncStatus.lastCodeChange.message}`);
    }
    
    if (this.syncStatus.lastDocsUpdate) {
      console.log(`最后文档更新: ${this.syncStatus.lastDocsUpdate.date.toLocaleString()}`);
    }
    
    if (this.syncStatus.syncLag > 0) {
      const lagDays = Math.floor(this.syncStatus.syncLag / 86400);
      const lagHours = Math.floor((this.syncStatus.syncLag % 86400) / 3600);
      console.log(`同步延迟: ${lagDays}天 ${lagHours}小时`);
    }
    
    if (this.syncStatus.outdatedSections.length > 0) {
      console.log('\n⚠️  可能过时的文档部分:');
      this.syncStatus.outdatedSections.forEach(section => {
        console.log(`  - ${section.section}: ${section.reason}`);
      });
    }
    
    if (this.syncStatus.missingDocs.length > 0) {
      console.log('\n📝 缺失的文档:');
      this.syncStatus.missingDocs.slice(0, 5).forEach(missing => {
        console.log(`  - ${missing.suggestion}`);
      });
      
      if (this.syncStatus.missingDocs.length > 5) {
        console.log(`  ... 还有 ${this.syncStatus.missingDocs.length - 5} 项`);
      }
    }
  }

  /**
   * 📝 生成Markdown报告
   */
  generateMarkdownReport() {
    const statusEmoji = {
      'synced': '🟢',
      'slightly-outdated': '🟡',
      'outdated': '🔴',
      'unknown': '⚪'
    };
    
    let report = `# 📊 文档同步状态报告\n\n`;
    report += `**状态**: ${statusEmoji[this.syncStatus.status]} ${this.syncStatus.status.toUpperCase()}\n`;
    report += `**生成时间**: ${new Date().toLocaleString('zh-CN')}\n\n`;
    
    if (this.syncStatus.lastCodeChange) {
      report += `## 📅 最近变更\n\n`;
      report += `- **最后代码变更**: ${this.syncStatus.lastCodeChange.date.toLocaleString()}\n`;
      report += `- **变更内容**: ${this.syncStatus.lastCodeChange.message}\n`;
      
      if (this.syncStatus.lastDocsUpdate) {
        report += `- **最后文档更新**: ${this.syncStatus.lastDocsUpdate.date.toLocaleString()}\n`;
      }
      
      if (this.syncStatus.syncLag > 0) {
        const lagDays = Math.floor(this.syncStatus.syncLag / 86400);
        const lagHours = Math.floor((this.syncStatus.syncLag % 86400) / 3600);
        report += `- **同步延迟**: ${lagDays}天 ${lagHours}小时\n`;
      }
      
      report += '\n';
    }
    
    if (this.syncStatus.outdatedSections.length > 0) {
      report += `## ⚠️  可能过时的文档\n\n`;
      this.syncStatus.outdatedSections.forEach(section => {
        report += `### ${section.section}\n`;
        report += `${section.reason}\n\n`;
        if (section.files && section.files.length > 0) {
          report += `相关文件:\n`;
          section.files.forEach(file => {
            report += `- \`${file}\`\n`;
          });
          report += '\n';
        }
      });
    }
    
    if (this.syncStatus.missingDocs.length > 0) {
      report += `## 📝 缺失的文档\n\n`;
      this.syncStatus.missingDocs.forEach(missing => {
        report += `- **${missing.type}**: ${missing.suggestion}\n`;
      });
      report += '\n';
    }
    
    report += `## 💡 建议\n\n`;
    
    if (this.syncStatus.status === 'synced') {
      report += `✅ 文档与代码保持同步，状态良好！\n`;
    } else {
      report += `📚 建议更新文档以匹配最新的代码变更\n`;
      report += `🔄 可以运行 \`npm run docs:generate\` 自动更新文档\n`;
    }
    
    fs.writeFileSync('docs-sync-report.md', report);
    console.log('\n📄 同步报告已生成: docs-sync-report.md');
  }

  /**
   * 🏷️  更新状态徽章
   */
  updateStatusBadge() {
    const readmePath = path.join(process.cwd(), 'README.md');
    
    if (!fs.existsSync(readmePath)) return;
    
    let content = fs.readFileSync(readmePath, 'utf8');
    
    const statusColors = {
      'synced': 'brightgreen',
      'slightly-outdated': 'yellow',
      'outdated': 'red',
      'unknown': 'lightgrey'
    };
    
    const statusTexts = {
      'synced': '同步',
      'slightly-outdated': '轻微过时',
      'outdated': '需要更新',
      'unknown': '未知'
    };
    
    const newBadge = `![文档状态](https://img.shields.io/badge/文档状态-${statusTexts[this.syncStatus.status]}-${statusColors[this.syncStatus.status]})`;
    
    // 替换现有的文档状态徽章
    const badgeRegex = /!\[文档状态\]\([^)]+\)/;
    if (badgeRegex.test(content)) {
      content = content.replace(badgeRegex, newBadge);
    } else {
      // 如果没有找到徽章，在README开头添加
      const titleMatch = content.match(/^#\s+.+$/m);
      if (titleMatch) {
        const insertIndex = content.indexOf(titleMatch[0]) + titleMatch[0].length;
        content = content.slice(0, insertIndex) + '\n\n' + newBadge + '\n' + content.slice(insertIndex);
      }
    }
    
    fs.writeFileSync(readmePath, content);
    console.log(`🏷️  状态徽章已更新: ${statusTexts[this.syncStatus.status]}`);
  }
}

// 运行监控
if (require.main === module) {
  const monitor = new DocsSyncMonitor();
  monitor.monitor().catch(error => {
    console.error('💥 监控失败:', error);
    process.exit(1);
  });
}

module.exports = DocsSyncMonitor;
