#!/usr/bin/env node

/**
 * 🔍 API文档验证脚本
 * 检查README.md中的API文档是否与实际路由定义同步
 */

const fs = require('fs');
const path = require('path');

class ApiDocValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.routeFiles = [];
    this.documentedApis = [];
    this.actualRoutes = [];
  }

  /**
   * 🚀 主验证流程
   */
  async validate() {
    console.log('🔍 开始验证API文档...\n');
    
    try {
      // 1. 扫描路由文件
      await this.scanRouteFiles();
      
      // 2. 解析README中的API文档
      await this.parseDocumentedApis();
      
      // 3. 提取实际路由定义
      await this.extractActualRoutes();
      
      // 4. 对比验证
      await this.compareApiDocumentation();
      
      // 5. 生成报告
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 验证过程中发生错误:', error.message);
      process.exit(1);
    }
  }

  /**
   * 📁 扫描路由文件
   */
  async scanRouteFiles() {
    const routesDir = path.join(process.cwd(), 'src/routes');
    
    if (!fs.existsSync(routesDir)) {
      this.warnings.push('⚠️  路由目录不存在: src/routes');
      return;
    }

    const files = fs.readdirSync(routesDir);
    this.routeFiles = files
      .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
      .map(file => path.join(routesDir, file));

    console.log(`📁 发现 ${this.routeFiles.length} 个路由文件`);
  }

  /**
   * 📖 解析README中的API文档
   */
  async parseDocumentedApis() {
    const readmePath = path.join(process.cwd(), 'README.md');
    
    if (!fs.existsSync(readmePath)) {
      this.errors.push('❌ README.md 文件不存在');
      return;
    }

    const content = fs.readFileSync(readmePath, 'utf8');
    
    // 匹配HTTP请求示例
    const httpRegex = /```http\s*\n(.*?)\n```/gs;
    let match;
    
    while ((match = httpRegex.exec(content)) !== null) {
      const httpBlock = match[1];
      const lines = httpBlock.split('\n');
      const requestLine = lines[0].trim();
      
      if (requestLine.match(/^(GET|POST|PUT|DELETE|PATCH)\s+/)) {
        const [method, endpoint] = requestLine.split(/\s+/);
        this.documentedApis.push({
          method: method.toUpperCase(),
          endpoint: endpoint,
          raw: httpBlock
        });
      }
    }

    console.log(`📖 在README中发现 ${this.documentedApis.length} 个API文档`);
  }

  /**
   * 🔍 提取实际路由定义
   */
  async extractActualRoutes() {
    for (const routeFile of this.routeFiles) {
      try {
        const content = fs.readFileSync(routeFile, 'utf8');
        
        // 匹配路由定义 (简化版正则，实际项目可能需要更复杂的解析)
        const routeRegex = /router\.(get|post|put|delete|patch)\s*\(\s*['"`]([^'"`]+)['"`]/gi;
        let match;
        
        while ((match = routeRegex.exec(content)) !== null) {
          const method = match[1].toUpperCase();
          let endpoint = match[2];
          
          // 处理路由前缀 (假设有 /api/v1 前缀)
          if (!endpoint.startsWith('/api')) {
            endpoint = '/api/v1' + (endpoint.startsWith('/') ? '' : '/') + endpoint;
          }
          
          this.actualRoutes.push({
            method,
            endpoint,
            file: path.basename(routeFile)
          });
        }
      } catch (error) {
        this.warnings.push(`⚠️  无法解析路由文件 ${routeFile}: ${error.message}`);
      }
    }

    console.log(`🔍 在代码中发现 ${this.actualRoutes.length} 个实际路由`);
  }

  /**
   * ⚖️  对比API文档与实际路由
   */
  async compareApiDocumentation() {
    // 检查文档中的API是否在代码中存在
    for (const docApi of this.documentedApis) {
      const found = this.actualRoutes.find(route => 
        route.method === docApi.method && 
        this.normalizeEndpoint(route.endpoint) === this.normalizeEndpoint(docApi.endpoint)
      );
      
      if (!found) {
        this.errors.push(`❌ 文档中的API在代码中未找到: ${docApi.method} ${docApi.endpoint}`);
      }
    }

    // 检查代码中的API是否在文档中存在
    for (const route of this.actualRoutes) {
      const found = this.documentedApis.find(docApi => 
        docApi.method === route.method && 
        this.normalizeEndpoint(docApi.endpoint) === this.normalizeEndpoint(route.endpoint)
      );
      
      if (!found) {
        this.warnings.push(`⚠️  代码中的API在文档中未找到: ${route.method} ${route.endpoint} (${route.file})`);
      }
    }
  }

  /**
   * 🔧 标准化端点路径
   */
  normalizeEndpoint(endpoint) {
    return endpoint
      .replace(/:\w+/g, '{id}')  // 将 :id 替换为 {id}
      .replace(/\/+/g, '/')      // 合并多个斜杠
      .replace(/\/$/, '');       // 移除末尾斜杠
  }

  /**
   * 📊 生成验证报告
   */
  generateReport() {
    console.log('\n📊 API文档验证报告');
    console.log('='.repeat(50));
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('✅ 所有API文档都与代码同步！');
      return;
    }

    if (this.errors.length > 0) {
      console.log('\n❌ 错误:');
      this.errors.forEach(error => console.log(`  ${error}`));
    }

    if (this.warnings.length > 0) {
      console.log('\n⚠️  警告:');
      this.warnings.forEach(warning => console.log(`  ${warning}`));
    }

    console.log('\n📈 统计信息:');
    console.log(`  📖 文档中的API: ${this.documentedApis.length}`);
    console.log(`  🔍 代码中的路由: ${this.actualRoutes.length}`);
    console.log(`  ❌ 错误数量: ${this.errors.length}`);
    console.log(`  ⚠️  警告数量: ${this.warnings.length}`);

    // 如果有错误，退出码为1
    if (this.errors.length > 0) {
      console.log('\n💡 建议: 请更新README.md中的API文档以匹配实际的路由定义');
      process.exit(1);
    }
  }
}

// 运行验证
if (require.main === module) {
  const validator = new ApiDocValidator();
  validator.validate().catch(error => {
    console.error('💥 验证失败:', error);
    process.exit(1);
  });
}

module.exports = ApiDocValidator;
