#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 运行预提交检查..."

# 代码质量检查
echo "📝 检查代码质量..."
npm run lint || exit 1

# TypeScript 类型检查
echo "🔍 TypeScript 类型检查..."
npm run typecheck || exit 1

# 文档同步检查
echo "📚 检查文档同步..."
npm run docs:validate || echo "⚠️  文档验证警告，请检查API文档是否需要更新"

# 文档质量检查
echo "📊 检查文档质量..."
npm run docs:quality || echo "⚠️  文档质量检查完成，请查看报告"

# 更新项目统计
echo "📈 更新项目统计..."
npm run docs:stats

echo "✅ 预提交检查完成！"
